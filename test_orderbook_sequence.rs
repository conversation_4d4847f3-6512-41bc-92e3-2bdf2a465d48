use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

// 模拟全局状态
struct MockAppState {
    update_id_counter: u64,
}

impl MockAppState {
    fn new() -> Self {
        Self { update_id_counter: 0 }
    }

    fn get_next_update_id(&mut self) -> u64 {
        self.update_id_counter += 1;
        self.update_id_counter
    }
}

// 模拟数据流
fn simulate_data_streaming() {
    let mut state = MockAppState::new();
    
    // 模拟三个并发数据流（quotes, trades, depth）
    let mut handles = vec![];
    
    // 模拟 depth 数据流（生成 orderbook）
    let state_clone = Arc::new(Mutex::new(state));
    
    let depth_handle = thread::spawn(move || {
        let mut local_state = state_clone.lock().unwrap();
        for i in 0..10 {
            let update_id = local_state.get_next_update_id();
            println!("Depth stream generated orderbook with update_id: {}", update_id);
            
            // 模拟处理延迟
            thread::sleep(Duration::from_millis(10));
        }
    });
    handles.push(depth_handle);
    
    // 模拟 quotes 数据流（生成 BBO）
    let state_clone2 = Arc::new(Mutex::new(MockAppState::new()));
    let quotes_handle = thread::spawn(move || {
        let mut local_state = state_clone2.lock().unwrap();
        for i in 0..10 {
            let update_id = local_state.get_next_update_id();
            println!("Quotes stream generated BBO with update_id: {}", update_id);
            
            // 模拟不同的处理延迟
            thread::sleep(Duration::from_millis(15));
        }
    });
    handles.push(quotes_handle);
    
    // 模拟 trades 数据流
    let state_clone3 = Arc::new(Mutex::new(MockAppState::new()));
    let trades_handle = thread::spawn(move || {
        let mut local_state = state_clone3.lock().unwrap();
        for i in 0..10 {
            let update_id = local_state.get_next_update_id();
            println!("Trades stream generated trade with update_id: {}", update_id);
            
            // 模拟不同的处理延迟
            thread::sleep(Duration::from_millis(20));
        }
    });
    handles.push(trades_handle);
    
    // 等待所有任务完成
    for handle in handles {
        handle.join().unwrap();
    }
}

fn main() {
    println!("=== 测试 orderbook update_id 不连续问题 ===");
    println!("问题分析：");
    println!("1. 系统中有三个并发数据流：quotes, trades, depth");
    println!("2. 每个数据流都有自己的 update_id 计数器实例");
    println!("3. 当多个数据流同时调用 get_next_update_id() 时，会产生竞争条件");
    println!("4. 导致 update_id 不连续");
    println!();
    
    simulate_data_streaming();
    
    println!();
    println!("=== 解决方案 ===");
    println!("1. 使用全局共享的 update_id 计数器");
    println!("2. 确保计数器操作的原子性");
    println!("3. 或者为不同类型的数据使用不同的计数器");
} 