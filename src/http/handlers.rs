use crate::backtest_summary::{HtmlGenerator, RealTimeReportGenerator};
use crate::types::Trade;
use crate::{config::ConfigManager, matching::orderbook::OrderBookStatus};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::convert::Infallible;
use warp::{http::StatusCode, Reply};

/// API响应结构
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
        }
    }
}

/// 健康检查响应
#[derive(Debug, Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub version: String,
}

/// 配置信息响应
#[derive(Debug, Serialize)]
pub struct ConfigResponse {
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: chrono::DateTime<chrono::Utc>,
    pub websocket_port: u16,
    pub http_port: u16,
}

/// Binance API 兼容的交易所信息响应
#[derive(Debug, Serialize)]
pub struct ExchangeInfoResponse {
    pub timezone: String,
    #[serde(rename = "serverTime")]
    pub server_time: i64,
    #[serde(rename = "futuresType")]
    pub futures_type: String,
    #[serde(rename = "rateLimits")]
    pub rate_limits: Vec<RateLimit>,
    #[serde(rename = "exchangeFilters")]
    pub exchange_filters: Vec<serde_json::Value>,
    pub assets: Vec<AssetInfo>,
    pub symbols: Vec<SymbolInfo>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sors: Option<Vec<SorInfo>>,
}

/// 资产信息
#[derive(Debug, Serialize)]
pub struct AssetInfo {
    pub asset: String,
    #[serde(rename = "marginAvailable")]
    pub margin_available: bool,
    #[serde(rename = "autoAssetExchange")]
    pub auto_asset_exchange: String,
}

/// SOR (Smart Order Routing) 信息
#[derive(Debug, Serialize)]
pub struct SorInfo {
    #[serde(rename = "baseAsset")]
    pub base_asset: String,
    pub symbols: Vec<String>,
}

#[derive(Debug, Serialize)]
pub struct RateLimit {
    #[serde(rename = "rateLimitType")]
    pub rate_limit_type: String,
    pub interval: String,
    #[serde(rename = "intervalNum")]
    pub interval_num: i32,
    pub limit: i32,
}

#[derive(Debug, Serialize)]
pub struct SymbolInfo {
    pub symbol: String,
    pub pair: String,
    #[serde(rename = "contractType")]
    pub contract_type: String,
    #[serde(rename = "deliveryDate")]
    pub delivery_date: i64,
    #[serde(rename = "onboardDate")]
    pub onboard_date: i64,
    pub status: String,
    #[serde(rename = "maintMarginPercent")]
    pub maint_margin_percent: String,
    #[serde(rename = "requiredMarginPercent")]
    pub required_margin_percent: String,
    #[serde(rename = "baseAsset")]
    pub base_asset: String,
    #[serde(rename = "quoteAsset")]
    pub quote_asset: String,
    #[serde(rename = "marginAsset")]
    pub margin_asset: String,
    #[serde(rename = "pricePrecision")]
    pub price_precision: i32,
    #[serde(rename = "quantityPrecision")]
    pub quantity_precision: i32,
    #[serde(rename = "baseAssetPrecision")]
    pub base_asset_precision: i32,
    #[serde(rename = "quotePrecision")]
    pub quote_precision: i32,
    #[serde(rename = "underlyingType")]
    pub underlying_type: String,
    #[serde(rename = "underlyingSubType")]
    pub underlying_sub_type: Vec<String>,
    #[serde(rename = "triggerProtect")]
    pub trigger_protect: String,
    #[serde(rename = "liquidationFee")]
    pub liquidation_fee: String,
    #[serde(rename = "marketTakeBound")]
    pub market_take_bound: String,
    #[serde(rename = "maxMoveOrderLimit")]
    pub max_move_order_limit: i32,
    pub filters: Vec<serde_json::Value>,
    #[serde(rename = "orderTypes")]
    pub order_types: Vec<String>,
    #[serde(rename = "timeInForce")]
    pub time_in_force: Vec<String>,
    #[serde(rename = "permissionSets")]
    pub permission_sets: Vec<String>,
}

/// 统计信息响应
#[derive(Debug, Serialize)]
pub struct StatsResponse {
    pub connected_clients: usize,
    pub total_trades: u64,
    pub orderbook_updates: u64,
    pub uptime_seconds: u64,
}

/// 数据流状态响应
#[derive(Debug, Serialize)]
pub struct DataStreamStatusResponse {
    pub status: String,
    pub config: DataStreamConfigResponse,
    pub stats: DataStreamStatsResponse,
}

/// 数据流配置响应
#[derive(Debug, Serialize, Deserialize)]
pub struct DataStreamConfigResponse {
    pub read_interval_ms: u64,
    pub realtime_simulation: bool,
    pub buffer_size: usize,
}

/// 数据流统计响应
#[derive(Debug, Serialize)]
pub struct DataStreamStatsResponse {
    pub messages_processed: u64,
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    pub last_processed_time: Option<chrono::DateTime<chrono::Utc>>,
    pub error_count: u64,
}

/// 解析symbol字符串以提取base和quote资产
/// 支持多种格式：BTCUSDT, BTC-USDT, BTC-USDT-SWAP等
fn parse_symbol_assets(symbol: &str) -> (String, String) {
    // 处理不同的symbol格式
    if symbol.contains('-') {
        // 处理格式如 BTC-USDT 或 BTC-USDT-SWAP
        let parts: Vec<&str> = symbol.split('-').collect();
        if parts.len() >= 2 {
            return (parts[0].to_string(), parts[1].to_string());
        }
    } else {
        // 处理格式如 BTCUSDT
        // 尝试常见的quote资产分割
        let common_quotes = ["USDT", "USDC", "BUSD", "BTC", "ETH", "BNB"];
        for quote in &common_quotes {
            if symbol.ends_with(quote) && symbol.len() > quote.len() {
                let base = &symbol[..symbol.len() - quote.len()];
                return (base.to_string(), quote.to_string());
            }
        }
    }

    // 如果无法解析，返回默认值
    ("BTC".to_string(), "USDT".to_string())
}

/// 创建符合Binance Futures API的symbol filters
fn create_futures_symbol_filters() -> Vec<serde_json::Value> {
    vec![
        serde_json::json!({
            "maxPrice": "4529764",
            "tickSize": "0.10",
            "filterType": "PRICE_FILTER",
            "minPrice": "556.80"
        }),
        serde_json::json!({
            "stepSize": "0.001",
            "minQty": "0.001",
            "filterType": "LOT_SIZE",
            "maxQty": "1000"
        }),
        serde_json::json!({
            "minQty": "0.001",
            "stepSize": "0.001",
            "filterType": "MARKET_LOT_SIZE",
            "maxQty": "120"
        }),
        serde_json::json!({
            "limit": 200,
            "filterType": "MAX_NUM_ORDERS"
        }),
        serde_json::json!({
            "filterType": "MAX_NUM_ALGO_ORDERS",
            "limit": 10
        }),
        serde_json::json!({
            "notional": "100",
            "filterType": "MIN_NOTIONAL"
        }),
        serde_json::json!({
            "multiplierUp": "1.0500",
            "multiplierDown": "0.9500",
            "filterType": "PERCENT_PRICE",
            "multiplierDecimal": "4"
        }),
        serde_json::json!({
            "positionControlSide": "NONE",
            "filterType": "POSITION_RISK_CONTROL"
        }),
    ]
}

/// 健康检查处理器
pub async fn health_handler() -> Result<impl Reply, Infallible> {
    tracing::info!("🔍 Health check request received");
    let response = ApiResponse::success(HealthResponse {
        status: "healthy".to_string(),
        timestamp: chrono::Utc::now(),
        version: env!("CARGO_PKG_VERSION").to_string(),
    });

    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 获取配置信息处理器
pub async fn config_handler() -> Result<impl Reply, Infallible> {
    match ConfigManager::get() {
        Ok(config) => {
            let response = ApiResponse::success(ConfigResponse {
                start_time: config.start_time,
                end_time: config.end_time,
                websocket_port: config.websocket_port,
                http_port: config.http_port,
            });

            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::OK,
            ))
        }
        Err(e) => {
            let response: ApiResponse<ConfigResponse> =
                ApiResponse::error(format!("Failed to get config: {}", e));
            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::INTERNAL_SERVER_ERROR,
            ))
        }
    }
}

/// Binance API 兼容的交易所信息处理器
pub async fn exchange_info_handler() -> Result<impl Reply, Infallible> {
    tracing::debug!("🔍 Exchange info request received at /fapi/v1/exchangeInfo");
    let server_time = chrono::Utc::now().timestamp_millis();

    // 从配置中获取支持的symbols
    let supported_symbols = match ConfigManager::get() {
        Ok(config) => config.account.supported_symbols,
        Err(_) => vec!["BTCUSDT".to_string()], // 如果无法获取配置，使用默认值
    };

    // 创建模拟的交易所信息，兼容 Binance API
    let exchange_info = ExchangeInfoResponse {
        timezone: "UTC".to_string(),
        server_time,
        futures_type: "U_MARGINED".to_string(),
        rate_limits: vec![
            RateLimit {
                rate_limit_type: "REQUEST_WEIGHT".to_string(),
                interval: "MINUTE".to_string(),
                interval_num: 1,
                limit: 2400,
            },
            RateLimit {
                rate_limit_type: "ORDERS".to_string(),
                interval: "MINUTE".to_string(),
                interval_num: 1,
                limit: 1200,
            },
            RateLimit {
                rate_limit_type: "ORDERS".to_string(),
                interval: "SECOND".to_string(),
                interval_num: 10,
                limit: 300,
            },
        ],
        exchange_filters: vec![], // Binance futures API returns empty array
        assets: vec![
            AssetInfo {
                asset: "USDT".to_string(),
                margin_available: true,
                auto_asset_exchange: "-10000".to_string(),
            },
            AssetInfo {
                asset: "BTC".to_string(),
                margin_available: true,
                auto_asset_exchange: "-0.10000000".to_string(),
            },
            AssetInfo {
                asset: "BNB".to_string(),
                margin_available: true,
                auto_asset_exchange: "0".to_string(),
            },
            AssetInfo {
                asset: "ETH".to_string(),
                margin_available: true,
                auto_asset_exchange: "0".to_string(),
            },
        ],
        symbols: supported_symbols
            .iter()
            .map(|symbol| {
                // 解析symbol以提取base和quote资产
                let (base_asset, quote_asset) = parse_symbol_assets(symbol);

                SymbolInfo {
                    symbol: symbol.clone(),
                    pair: symbol.clone(),
                    contract_type: "PERPETUAL".to_string(),
                    delivery_date: 4133404800000,
                    onboard_date: 1569398400000,
                    status: "TRADING".to_string(),
                    maint_margin_percent: "2.5000".to_string(),
                    required_margin_percent: "5.0000".to_string(),
                    base_asset,
                    quote_asset: quote_asset.clone(),
                    margin_asset: quote_asset,
                    price_precision: 2,
                    quantity_precision: 3,
                    base_asset_precision: 8,
                    quote_precision: 8,
                    underlying_type: "COIN".to_string(),
                    underlying_sub_type: vec!["PoW".to_string()],
                    trigger_protect: "0.0500".to_string(),
                    liquidation_fee: "0.012500".to_string(),
                    market_take_bound: "0.05".to_string(),
                    max_move_order_limit: 10000,
                    filters: create_futures_symbol_filters(),
                    order_types: vec![
                        "LIMIT".to_string(),
                        "MARKET".to_string(),
                        "STOP".to_string(),
                        "STOP_MARKET".to_string(),
                        "TAKE_PROFIT".to_string(),
                        "TAKE_PROFIT_MARKET".to_string(),
                        "TRAILING_STOP_MARKET".to_string(),
                    ],
                    time_in_force: vec![
                        "GTC".to_string(),
                        "IOC".to_string(),
                        "FOK".to_string(),
                        "GTX".to_string(),
                        "GTD".to_string(),
                    ],
                    permission_sets: vec!["GRID".to_string(), "COPY".to_string()],
                }
            })
            .collect(),
        sors: Some(vec![SorInfo {
            base_asset: "BTC".to_string(),
            symbols: vec!["BTCUSDT".to_string(), "BTCUSDC".to_string()],
        }]),
    };

    // 直接返回exchangeInfo对象，与真实Binance API保持一致
    tracing::debug!("exchange_info {:?}", exchange_info);
    Ok(warp::reply::with_status(
        warp::reply::json(&exchange_info),
        StatusCode::OK,
    ))
}

/// 自定义订单簿快照格式，用于数组序列化
#[derive(Debug, Serialize)]
struct OrderBookSnapshotArray {
    #[serde(rename = "E")]
    timestamp: u64,
    #[serde(rename = "lastUpdateId")]
    update_id: u64,
    bids: Vec<[String; 2]>,
    asks: Vec<[String; 2]>,
}

/// 获取订单簿快照处理器
pub async fn orderbook_handler() -> Result<impl Reply, Infallible> {
    // 从全局状态获取订单簿
    let orderbook_opt = crate::state::get_orderbook().await;

    if let Some(orderbook) = orderbook_opt {
        // 获取订单簿快照
        let mut orderbook_guard = orderbook.lock().await;
        let (bids, asks, update_time, update_id) = orderbook_guard.snapshot();
        orderbook_guard.set_status(OrderBookStatus::HttpPushed);
        drop(orderbook_guard); // 释放锁

        // 转换为数组格式
        let bids_array: Vec<[String; 2]> = bids
            .iter()
            .map(|(price, qty)| [format!("{:.8}", price.value()), format!("{:.8}", qty)])
            .collect();

        let asks_array: Vec<[String; 2]> = asks
            .iter()
            .map(|(price, qty)| [format!("{:.8}", price.value()), format!("{:.8}", qty)])
            .collect();

        let snapshot = OrderBookSnapshotArray {
            timestamp: update_time,
            update_id: update_id,
            bids: bids_array,
            asks: asks_array,
        };

        tracing::info!("Orderbook snapshot: {:?}", snapshot);
        Ok(warp::reply::with_status(
            warp::reply::json(&snapshot),
            StatusCode::OK,
        ))
    } else {
        // 如果没有可用的订单簿，返回空的快照
        let snapshot = OrderBookSnapshotArray {
            timestamp: 0,
            update_id: 0,
            bids: Vec::new(),
            asks: Vec::new(),
        };

        Ok(warp::reply::with_status(
            warp::reply::json(&snapshot),
            StatusCode::OK,
        ))
    }
}

pub async fn listen_key_handler() -> Result<impl Reply, Infallible> {
    let body = serde_json::json!({
        "listenKey": "test_listen_key"
    });
    Ok(warp::reply::with_status(
        warp::reply::json(&body),
        StatusCode::OK,
    ))
}

/// 获取最近交易处理器
pub async fn trades_handler() -> Result<impl Reply, Infallible> {
    // TODO: 从撮合引擎获取实际的交易数据
    // 这里是占位实现
    let trades: Vec<Trade> = Vec::new();

    let response = ApiResponse::success(trades);
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 获取统计信息处理器
pub async fn stats_handler() -> Result<impl Reply, Infallible> {
    // TODO: 从各个模块收集实际的统计数据
    // 这里是占位实现
    let stats = StatsResponse {
        connected_clients: 0,
        total_trades: 0,
        orderbook_updates: 0,
        uptime_seconds: 0,
    };

    let response = ApiResponse::success(stats);
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 获取技术指标处理器
pub async fn indicators_handler() -> Result<impl Reply, Infallible> {
    // TODO: 从技术指标模块获取数据
    // 这里是占位实现
    let indicators: HashMap<String, f64> = HashMap::new();

    let response = ApiResponse::success(indicators);
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 获取数据流状态处理器
pub async fn datastream_status_handler() -> Result<impl Reply, Infallible> {
    if let Some(controller) = crate::state::get_data_stream_controller().await {
        let ctl = controller.lock().await;
        let status = ctl.get_status().await;
        let config = ctl.get_config().await;
        let stats = ctl.get_stats().await;

        let status_str = match status {
            crate::data::DataStreamStatus::Stopped => "stopped",
            crate::data::DataStreamStatus::Running => "running",
            crate::data::DataStreamStatus::Paused => "paused",
            crate::data::DataStreamStatus::Error(_) => "error",
        }
        .to_string();

        let status_response = DataStreamStatusResponse {
            status: status_str,
            config: DataStreamConfigResponse {
                read_interval_ms: config.read_interval_ms,
                realtime_simulation: config.realtime_simulation,
                buffer_size: config.buffer_size,
            },
            stats: DataStreamStatsResponse {
                messages_processed: stats.messages_processed,
                start_time: stats.start_time,
                last_processed_time: stats.last_processed_time,
                error_count: stats.error_count,
            },
        };

        let response = ApiResponse::success(status_response);
        Ok(warp::reply::with_status(
            warp::reply::json(&response),
            StatusCode::OK,
        ))
    } else {
        let response: ApiResponse<()> =
            ApiResponse::error("Data stream controller not available".to_string());
        Ok(warp::reply::with_status(
            warp::reply::json(&response),
            StatusCode::INTERNAL_SERVER_ERROR,
        ))
    }
}

/// 启动数据流处理器
pub async fn datastream_start_handler() -> Result<impl Reply, Infallible> {
    if let Some(controller) = crate::state::get_data_stream_controller().await {
        match controller.lock().await.start().await {
            Ok(_) => {
                let response = ApiResponse::success("Data stream started successfully");
                Ok(warp::reply::with_status(
                    warp::reply::json(&response),
                    StatusCode::OK,
                ))
            }
            Err(e) => {
                let response: ApiResponse<()> =
                    ApiResponse::error(format!("Failed to start data stream: {}", e));
                Ok(warp::reply::with_status(
                    warp::reply::json(&response),
                    StatusCode::INTERNAL_SERVER_ERROR,
                ))
            }
        }
    } else {
        let response: ApiResponse<()> =
            ApiResponse::error("Data stream controller not available".to_string());
        Ok(warp::reply::with_status(
            warp::reply::json(&response),
            StatusCode::INTERNAL_SERVER_ERROR,
        ))
    }
}

/// 停止数据流处理器
pub async fn datastream_stop_handler() -> Result<impl Reply, Infallible> {
    // TODO: 调用实际的数据流控制器停止方法
    // 这里是占位实现
    let response = ApiResponse::success("Data stream stop command sent");
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 暂停数据流处理器
pub async fn datastream_pause_handler() -> Result<impl Reply, Infallible> {
    // TODO: 调用实际的数据流控制器暂停方法
    // 这里是占位实现
    let response = ApiResponse::success("Data stream pause command sent");
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 恢复数据流处理器
pub async fn datastream_resume_handler() -> Result<impl Reply, Infallible> {
    // TODO: 调用实际的数据流控制器恢复方法
    // 这里是占位实现
    let response = ApiResponse::success("Data stream resume command sent");
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 更新数据流配置处理器
pub async fn datastream_config_handler(
    config: DataStreamConfigResponse,
) -> Result<impl Reply, Infallible> {
    // TODO: 调用实际的数据流控制器更新配置方法
    // 这里是占位实现
    tracing::info!("Received config update: {:?}", config);
    let response = ApiResponse::success("Data stream configuration updated");
    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 错误处理器
pub async fn handle_rejection(err: warp::Rejection) -> Result<impl Reply, Infallible> {
    let code;
    let message;

    if err.is_not_found() {
        code = StatusCode::NOT_FOUND;
        message = "Not Found";
    } else if let Some(_) = err.find::<warp::filters::body::BodyDeserializeError>() {
        code = StatusCode::BAD_REQUEST;
        message = "Invalid Body";
    } else if let Some(_) = err.find::<warp::reject::MethodNotAllowed>() {
        code = StatusCode::METHOD_NOT_ALLOWED;
        message = "Method Not Allowed";
    } else {
        tracing::error!("Unhandled rejection: {:?}", err);
        code = StatusCode::INTERNAL_SERVER_ERROR;
        message = "Internal Server Error";
    }

    let response: ApiResponse<()> = ApiResponse::error(message.to_string());
    Ok(warp::reply::with_status(warp::reply::json(&response), code))
}

/// Binance Futures Account API 数据结构

/// 账户资产信息
#[derive(Debug, Serialize)]
pub struct FuturesAsset {
    pub asset: String,
    #[serde(rename = "walletBalance")]
    pub wallet_balance: String,
    #[serde(rename = "unrealizedProfit")]
    pub unrealized_profit: String,
    #[serde(rename = "marginBalance")]
    pub margin_balance: String,
    #[serde(rename = "maintMargin")]
    pub maint_margin: String,
    #[serde(rename = "initialMargin")]
    pub initial_margin: String,
    #[serde(rename = "positionInitialMargin")]
    pub position_initial_margin: String,
    #[serde(rename = "openOrderInitialMargin")]
    pub open_order_initial_margin: String,
    #[serde(rename = "crossWalletBalance")]
    pub cross_wallet_balance: String,
    #[serde(rename = "crossUnPnl")]
    pub cross_un_pnl: String,
    #[serde(rename = "availableBalance")]
    pub available_balance: String,
    #[serde(rename = "maxWithdrawAmount")]
    pub max_withdraw_amount: String,
    #[serde(rename = "updateTime")]
    pub update_time: i64,
}

/// 仓位信息
#[derive(Debug, Serialize)]
pub struct FuturesPosition {
    pub symbol: String,
    #[serde(rename = "positionSide")]
    pub position_side: String,
    #[serde(rename = "positionAmt")]
    pub position_amt: String,
    #[serde(rename = "unrealizedProfit")]
    pub unrealized_profit: String,
    #[serde(rename = "isolatedMargin")]
    pub isolated_margin: String,
    pub notional: String,
    #[serde(rename = "isolatedWallet")]
    pub isolated_wallet: String,
    #[serde(rename = "initialMargin")]
    pub initial_margin: String,
    #[serde(rename = "maintMargin")]
    pub maint_margin: String,
    #[serde(rename = "updateTime")]
    pub update_time: i64,
}

/// Binance Futures Account 响应
#[derive(Debug, Serialize)]
pub struct FuturesAccountResponse {
    #[serde(rename = "totalInitialMargin")]
    pub total_initial_margin: String,
    #[serde(rename = "totalMaintMargin")]
    pub total_maint_margin: String,
    #[serde(rename = "totalWalletBalance")]
    pub total_wallet_balance: String,
    #[serde(rename = "totalUnrealizedProfit")]
    pub total_unrealized_profit: String,
    #[serde(rename = "totalMarginBalance")]
    pub total_margin_balance: String,
    #[serde(rename = "totalPositionInitialMargin")]
    pub total_position_initial_margin: String,
    #[serde(rename = "totalOpenOrderInitialMargin")]
    pub total_open_order_initial_margin: String,
    #[serde(rename = "totalCrossWalletBalance")]
    pub total_cross_wallet_balance: String,
    #[serde(rename = "totalCrossUnPnl")]
    pub total_cross_un_pnl: String,
    #[serde(rename = "availableBalance")]
    pub available_balance: String,
    #[serde(rename = "maxWithdrawAmount")]
    pub max_withdraw_amount: String,
    pub assets: Vec<FuturesAsset>,
    pub positions: Vec<FuturesPosition>,
}

/// Binance Futures Account 处理器
pub async fn futures_account_handler() -> Result<impl Reply, Infallible> {
    match crate::state::get_account_manager().await {
        Some(account_manager) => {
            let manager = account_manager.lock().await;
            let account_summary = manager.get_account_summary();

            // 转换为Binance格式
            let response = convert_to_binance_format(account_summary);

            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::OK,
            ))
        }
        None => {
            let error_response =
                ApiResponse::<()>::error("Account manager not available".to_string());
            Ok(warp::reply::with_status(
                warp::reply::json(&error_response),
                StatusCode::INTERNAL_SERVER_ERROR,
            ))
        }
    }
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub(crate) struct AccountBalance {
    pub(crate) asset: String,
    balance: String,
    cross_un_pnl: String,
    max_withdraw_amount: String,
}

pub async fn futures_balance_handler() -> Result<impl Reply, Infallible> {
    match crate::state::get_account_manager().await {
        Some(account_manager) => {
            let manager = account_manager.lock().await;
            let account_summary = manager.get_account_summary();

            // 获取钱包总余额和未实现盈亏
            let total_balance = manager.get_total_balance("USDT"); // 使用公共方法
            let unrealized_pnl = account_summary.stats.unrealized_pnl;

            let response = AccountBalance {
                asset: "USDT".to_string(),
                balance: total_balance.to_string(), // 使用钱包总余额
                cross_un_pnl: unrealized_pnl.to_string(), // 使用实际的未实现盈亏
                max_withdraw_amount: account_summary.stats.available_balance.to_string(), // 最大可提取金额等于可用余额
            };

            Ok(warp::reply::with_status(
                warp::reply::json(&vec![response]),
                StatusCode::OK,
            ))
        }
        None => {
            let error_response =
                ApiResponse::<()>::error("Account manager not available".to_string());
            Ok(warp::reply::with_status(
                warp::reply::json(&error_response),
                StatusCode::INTERNAL_SERVER_ERROR,
            ))
        }
    }
}

pub async fn okx_root_handler() -> Result<impl Reply, Infallible> {
    Ok(warp::reply::with_status(
        warp::reply::json(&serde_json::json!({"status": "ok"})),
        StatusCode::OK,
    ))
}

/// OKX instruments 处理器 - 返回BTC-USDT交易对信息
pub async fn okx_instruments_handler() -> Result<impl Reply, Infallible> {
    let response = serde_json::json!({
        "code": "0",
        "msg": "",
        "data": [
            {
                "alias": "",
                "auctionEndTime": "",
                "baseCcy": "BTC",
                "category": "1",
                "contTdSwTime": "",
                "ctMult": "1",
                "ctType": "linear",
                "ctVal": "0.01",
                "ctValCcy": "BTC",
                "expTime": "",
                "futureSettlement": false,
                "instFamily": "BTC-USDT",
                "instId": "BTC-USDT",
                "instIdCode": 10459,
                "instType": "SPOT",
                "lever": "10",
                "listTime": "1573557408000",
                "lotSz": "0.00000001",
                "maxIcebergSz": "1********.****************",
                "maxLmtAmt": "20000000",
                "maxLmtSz": "1********",
                "maxMktAmt": "20000000",
                "maxMktSz": "12000",
                "maxStopSz": "12000",
                "maxTriggerSz": "1********.****************",
                "maxTwapSz": "1********.****************",
                "minSz": "0.00000001",
                "openType": "",
                "optType": "",
                "quoteCcy": "USDT",
                "ruleType": "normal",
                "settleCcy": "",
                "state": "live",
                "stk": "",
                "tickSz": "0.1",
                "tradeQuoteCcyList": [],
                "uly": ""
            }
        ]
    });

    Ok(warp::reply::with_status(
        warp::reply::json(&response),
        StatusCode::OK,
    ))
}

/// 将内部账户摘要转换为Binance格式
fn convert_to_binance_format(
    summary: crate::account::account::AccountSummary,
) -> FuturesAccountResponse {
    let current_time = chrono::Utc::now().timestamp_millis();

    // 转换资产信息
    let assets: Vec<FuturesAsset> = summary
        .balances
        .into_iter()
        .map(|(asset_name, balance)| {
            FuturesAsset {
                asset: asset_name,
                wallet_balance: format!("{:.8}", balance.total),
                unrealized_profit: "0.********".to_string(), // 现货资产没有未实现盈亏
                margin_balance: format!("{:.8}", balance.available),
                maint_margin: "0.********".to_string(),
                initial_margin: "0.********".to_string(),
                position_initial_margin: "0.********".to_string(),
                open_order_initial_margin: format!("{:.8}", balance.frozen),
                cross_wallet_balance: format!("{:.8}", balance.available),
                cross_un_pnl: "0.********".to_string(),
                available_balance: format!("{:.8}", balance.available),
                max_withdraw_amount: format!("{:.8}", balance.available),
                update_time: current_time,
            }
        })
        .collect();

    // 转换仓位信息
    let positions: Vec<FuturesPosition> = summary
        .positions
        .into_iter()
        .map(|pos| {
            FuturesPosition {
                symbol: pos.symbol,
                position_side: "BOTH".to_string(), // 单向持仓模式
                position_amt: format!("{:.8}", pos.quantity),
                unrealized_profit: format!("{:.8}", pos.unrealized_pnl),
                isolated_margin: "0.********".to_string(), // 全仓模式
                notional: format!("{:.8}", pos.notional_value),
                isolated_wallet: "0.********".to_string(),
                initial_margin: format!("{:.8}", pos.margin),
                maint_margin: format!("{:.8}", pos.margin * 0.5), // 简化计算
                update_time: current_time,
            }
        })
        .collect();

    FuturesAccountResponse {
        total_initial_margin: format!("{:.8}", summary.stats.used_margin),
        total_maint_margin: format!("{:.8}", summary.stats.used_margin * 0.5),
        total_wallet_balance: format!("{:.8}", summary.stats.total_value),
        total_unrealized_profit: format!("{:.8}", summary.stats.unrealized_pnl),
        total_margin_balance: format!(
            "{:.8}",
            summary.stats.available_balance + summary.stats.unrealized_pnl
        ),
        total_position_initial_margin: format!("{:.8}", summary.stats.used_margin),
        total_open_order_initial_margin: "0.********".to_string(),
        total_cross_wallet_balance: format!("{:.8}", summary.stats.available_balance),
        total_cross_un_pnl: format!("{:.8}", summary.stats.unrealized_pnl),
        available_balance: format!("{:.8}", summary.stats.available_balance),
        max_withdraw_amount: format!("{:.8}", summary.stats.available_balance),
        assets,
        positions,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_health_handler() {
        let result = health_handler().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_config_handler() {
        let result = config_handler().await;
        assert!(result.is_ok());
    }
}

/// 回测总结响应
#[derive(Debug, Serialize)]
pub struct BacktestSummaryResponse {
    pub success: bool,
    pub data: Option<String>, // HTML内容
    pub error: Option<String>,
}

/// 回测总结HTML响应
#[derive(Debug, Serialize)]
pub struct BacktestSummaryHtmlResponse {
    pub html: String,
}

/// 回测开始响应
#[derive(Debug, Serialize)]
pub struct BacktestStartResponse {
    pub success: bool,
    pub message: String,
    pub error: Option<String>,
}

/// 回测停止响应
#[derive(Debug, Serialize)]
pub struct BacktestStopResponse {
    pub success: bool,
    pub message: String,
    pub error: Option<String>,
}

/// 回测总结处理函数
pub async fn backtest_summary_handler() -> Result<impl Reply, Infallible> {
    use crate::state::get_backtest_recorder;

    match get_backtest_recorder().await {
        Some(recorder) => {
            let recorder = recorder.lock().await;

            // 检查是否有数据
            if recorder.bbo_records.is_empty()
                && recorder.orders.is_empty()
                && recorder.trades.is_empty()
            {
                let error_html = r#"
<!DOCTYPE html>
<html>
<head>
    <title>回测总结 - 无数据</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .error { color: #666; background: #f8f9fa; padding: 20px; border-radius: 5px; text-align: center; }
    </style>
</head>
<body>
    <h1>回测总结</h1>
    <div class="error">
        <h2>暂无数据</h2>
        <p>回测记录器中暂无数据。</p>
        <p>请先开始回测记录并等待数据积累。</p>
        <p><a href="/">返回首页</a></p>
    </div>
</body>
</html>
                "#.to_string();
                return Ok(warp::reply::with_header(
                    error_html,
                    "content-type",
                    "text/html; charset=utf-8",
                ));
            }

            // 生成回测总结
            match recorder.generate_summary() {
                Some(summary) => {
                    let html_content = HtmlGenerator::generate_summary_html(&summary);
                    // 直接返回HTML页面
                    Ok(warp::reply::with_header(
                        html_content,
                        "content-type",
                        "text/html; charset=utf-8",
                    ))
                }
                None => {
                    let error_html = r#"
<!DOCTYPE html>
<html>
<head>
    <title>回测总结 - 错误</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .error { color: red; background: #ffe6e6; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>回测总结</h1>
    <div class="error">
        <h2>错误</h2>
        <p>生成回测总结失败。</p>
        <p><a href="/">返回首页</a></p>
    </div>
</body>
</html>
                    "#
                    .to_string();
                    Ok(warp::reply::with_header(
                        error_html,
                        "content-type",
                        "text/html; charset=utf-8",
                    ))
                }
            }
        }
        None => {
            let error_html = r#"
<!DOCTYPE html>
<html>
<head>
    <title>回测总结 - 错误</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .error { color: red; background: #ffe6e6; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>回测总结</h1>
    <div class="error">
        <h2>错误</h2>
        <p>回测记录器未初始化。</p>
        <p><a href="/">返回首页</a></p>
    </div>
</body>
</html>
            "#
            .to_string();
            Ok(warp::reply::with_header(
                error_html,
                "content-type",
                "text/html; charset=utf-8",
            ))
        }
    }
}

/// 实时回测报告处理函数
pub async fn backtest_realtime_handler() -> Result<impl Reply, Infallible> {
    use crate::state::get_backtest_recorder;

    match get_backtest_recorder().await {
        Some(recorder) => {
            // 创建实时报告生成器（5秒更新间隔）
            let mut report_generator = RealTimeReportGenerator::new(5);

            // 生成实时报告
            let html_content = report_generator.generate_real_time_report(&recorder).await;

            // 直接返回HTML页面
            Ok(warp::reply::with_header(
                html_content,
                "content-type",
                "text/html; charset=utf-8",
            ))
        }
        None => {
            let error_html = r#"
<!DOCTYPE html>
<html>
<head>
    <title>实时回测报告 - 错误</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .error { color: red; background: #ffe6e6; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>实时回测报告</h1>
    <div class="error">
        <h2>错误</h2>
        <p>回测记录器未初始化。</p>
        <p><a href="/">返回首页</a></p>
    </div>
</body>
</html>
            "#
            .to_string();
            Ok(warp::reply::with_header(
                error_html,
                "content-type",
                "text/html; charset=utf-8",
            ))
        }
    }
}

/// 开始回测处理函数
pub async fn backtest_start_handler() -> Result<impl Reply, Infallible> {
    use crate::state::get_backtest_recorder;
    use chrono::Utc;

    match get_backtest_recorder().await {
        Some(recorder) => {
            let mut recorder = recorder.lock().await;
            let start_time = Utc::now();
            recorder.start_recording(start_time);

            let response = BacktestStartResponse {
                success: true,
                message: format!(
                    "Backtest started at {}",
                    start_time.format("%Y-%m-%d %H:%M:%S")
                ),
                error: None,
            };
            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::OK,
            ))
        }
        None => {
            let response = BacktestStartResponse {
                success: false,
                message: "".to_string(),
                error: Some("Backtest recorder not initialized".to_string()),
            };
            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::SERVICE_UNAVAILABLE,
            ))
        }
    }
}

/// 停止回测处理函数
pub async fn backtest_stop_handler() -> Result<impl Reply, Infallible> {
    use crate::state::get_backtest_recorder;
    use chrono::Utc;

    match get_backtest_recorder().await {
        Some(recorder) => {
            let mut recorder = recorder.lock().await;
            let end_time = Utc::now();
            recorder.stop_recording(end_time);

            let response = BacktestStopResponse {
                success: true,
                message: format!(
                    "Backtest stopped at {}",
                    end_time.format("%Y-%m-%d %H:%M:%S")
                ),
                error: None,
            };
            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::OK,
            ))
        }
        None => {
            let response = BacktestStopResponse {
                success: false,
                message: "".to_string(),
                error: Some("Backtest recorder not initialized".to_string()),
            };
            Ok(warp::reply::with_status(
                warp::reply::json(&response),
                StatusCode::SERVICE_UNAVAILABLE,
            ))
        }
    }
}
