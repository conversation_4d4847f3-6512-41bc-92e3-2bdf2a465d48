use crate::config::OrderLatencyConfig;
use crate::types::Order;
use crate::Result;
use rand::rngs::StdRng;
use rand::{Rng, SeedableRng};
use std::collections::VecDeque;
use tracing::{debug, warn};

/// 延迟订单项
#[derive(Debug, Clone)]
struct DelayedOrder {
    /// 订单
    order: Order,
    /// 执行时间戳（微秒）
    execution_timestamp: u64,
}

/// 订单延迟模拟器
/// 基于市场数据时间戳模拟订单执行延迟
#[derive(Debug)]
pub struct OrderLatencySimulator {
    /// 延迟订单队列（按执行时间排序）
    delayed_orders: VecDeque<DelayedOrder>,
    /// 配置
    config: OrderLatencyConfig,
    /// 随机数生成器
    rng: StdRng,
}

impl OrderLatencySimulator {
    /// 创建新的订单延迟模拟器
    pub fn new(config: OrderLatencyConfig) -> Self {
        Self {
            delayed_orders: VecDeque::new(),
            config,
            rng: StdRng::from_entropy(),
        }
    }

    /// 添加订单到延迟队列
    ///
    /// # 参数
    /// * `order` - 要延迟的订单
    /// * `current_market_timestamp` - 当前市场数据时间戳（微秒）
    pub fn add_order(&mut self, order: Order, current_market_timestamp: u64) -> Result<()> {
        if !self.config.enabled {
            // 如果未启用延迟模拟，直接返回错误，调用者应该立即处理订单
            return Err(crate::BacktestError::Config(
                "Order latency simulation is disabled".to_string(),
            ));
        }

        // 检查队列大小限制
        if self.delayed_orders.len() >= self.config.max_queue_size {
            warn!(
                "Delayed order queue reached size limit {}, removing oldest order",
                self.config.max_queue_size
            );
            self.delayed_orders.pop_front();
        }

        // 计算执行时间戳
        let latency = if self.config.random_latency {
            // 随机延迟：在 [latency_micros * 0.8, latency_micros * 1.2] 范围内
            let min_latency = (self.config.latency_micros as f64 * 0.8) as u64;
            let max_latency = (self.config.latency_micros as f64 * 1.2) as u64;
            self.rng.gen_range(min_latency..=max_latency)
        } else {
            self.config.latency_micros
        };

        let execution_timestamp = current_market_timestamp + latency;

        let order_id = order.id.clone(); // 保存order_id用于日志

        let delayed_order = DelayedOrder {
            order,
            execution_timestamp,
        };

        // 找到正确的插入位置以保持时间戳顺序
        let mut insert_pos = self.delayed_orders.len();
        for (i, existing_order) in self.delayed_orders.iter().enumerate().rev() {
            if delayed_order.execution_timestamp >= existing_order.execution_timestamp {
                insert_pos = i + 1;
                break;
            }
        }

        self.delayed_orders.insert(insert_pos, delayed_order);

        debug!(
            "Added order {} to delay queue, execution timestamp: {}, current queue size: {}",
            order_id,
            execution_timestamp,
            self.delayed_orders.len()
        );

        Ok(())
    }

    /// 获取可以执行的订单
    ///
    /// # 参数
    /// * `current_market_timestamp` - 当前市场数据时间戳（微秒）
    ///
    /// # 返回
    /// 返回所有执行时间戳小于等于当前时间戳的订单
    pub fn get_ready_orders(&mut self, current_market_timestamp: u64) -> Vec<Order> {
        let mut ready_orders = Vec::new();

        // 从队列前端取出所有可以执行的订单
        while let Some(delayed_order) = self.delayed_orders.front() {
            if delayed_order.execution_timestamp <= current_market_timestamp {
                let delayed_order = self.delayed_orders.pop_front().unwrap();
                debug!(
                    "Order {} is ready for execution (execution_timestamp: {}, current: {})",
                    delayed_order.order.id,
                    delayed_order.execution_timestamp,
                    current_market_timestamp
                );
                ready_orders.push(delayed_order.order);
            } else {
                break;
            }
        }

        ready_orders
    }

    /// 检查是否启用延迟模拟
    pub fn is_enabled(&self) -> bool {
        self.config.enabled
    }

    /// 获取队列中的订单数量
    pub fn queue_size(&self) -> usize {
        self.delayed_orders.len()
    }

    /// 获取下一个订单的执行时间戳
    pub fn next_execution_timestamp(&self) -> Option<u64> {
        self.delayed_orders
            .front()
            .map(|order| order.execution_timestamp)
    }

    /// 更新配置
    pub fn update_config(&mut self, config: OrderLatencyConfig) {
        debug!("Updating order latency config: {:?}", config);
        self.config = config;
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &OrderLatencyConfig {
        &self.config
    }

    /// 清空延迟队列
    pub fn clear(&mut self) {
        debug!(
            "Clearing delayed order queue, {} orders removed",
            self.delayed_orders.len()
        );
        self.delayed_orders.clear();
    }

    /// 获取队列统计信息
    pub fn get_stats(&self) -> OrderLatencyStats {
        let mut min_timestamp = None;
        let mut max_timestamp = None;

        for delayed_order in &self.delayed_orders {
            let timestamp = delayed_order.execution_timestamp;
            min_timestamp = Some(min_timestamp.map_or(timestamp, |min: u64| min.min(timestamp)));
            max_timestamp = Some(max_timestamp.map_or(timestamp, |max: u64| max.max(timestamp)));
        }

        OrderLatencyStats {
            queue_size: self.delayed_orders.len(),
            enabled: self.config.enabled,
            latency_micros: self.config.latency_micros,
            random_latency: self.config.random_latency,
            min_execution_timestamp: min_timestamp,
            max_execution_timestamp: max_timestamp,
        }
    }
}

/// 订单延迟统计信息
#[derive(Debug, Clone)]
pub struct OrderLatencyStats {
    /// 队列大小
    pub queue_size: usize,
    /// 是否启用
    pub enabled: bool,
    /// 配置的延迟时间（微秒）
    pub latency_micros: u64,
    /// 是否启用随机延迟
    pub random_latency: bool,
    /// 最小执行时间戳
    pub min_execution_timestamp: Option<u64>,
    /// 最大执行时间戳
    pub max_execution_timestamp: Option<u64>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{OrderSide, OrderStatus, OrderType, Price};
    use chrono::Utc;

    fn create_test_order(id: &str) -> Order {
        Order {
            id: id.to_string(),
            client_order_id: format!("client_{}", id),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(50000.0)),
            quantity: 1.0,
            status: OrderStatus::Pending,
            timestamp: Utc::now(),
            execution_info: None,
        }
    }

    #[test]
    fn test_order_latency_simulator_disabled() {
        let config = OrderLatencyConfig {
            enabled: false,
            latency_micros: 3000,
            max_queue_size: 100,
            random_latency: false,
        };

        let mut simulator = OrderLatencySimulator::new(config);
        let order = create_test_order("test1");

        // 当禁用时，添加订单应该返回错误
        let result = simulator.add_order(order, 1000000);
        assert!(result.is_err());
    }

    #[test]
    fn test_order_latency_simulator_basic() {
        let config = OrderLatencyConfig {
            enabled: true,
            latency_micros: 3000,
            max_queue_size: 100,
            random_latency: false,
        };

        let mut simulator = OrderLatencySimulator::new(config);
        let order = create_test_order("test1");
        let current_timestamp = 1000000;

        // 添加订单
        simulator.add_order(order, current_timestamp).unwrap();
        assert_eq!(simulator.queue_size(), 1);

        // 在延迟时间之前，不应该有可执行的订单
        let ready_orders = simulator.get_ready_orders(current_timestamp + 2000);
        assert_eq!(ready_orders.len(), 0);

        // 在延迟时间之后，应该有可执行的订单
        let ready_orders = simulator.get_ready_orders(current_timestamp + 3000);
        assert_eq!(ready_orders.len(), 1);
        assert_eq!(ready_orders[0].id, "test1");
        assert_eq!(simulator.queue_size(), 0);
    }

    #[test]
    fn test_order_latency_simulator_multiple_orders() {
        let config = OrderLatencyConfig {
            enabled: true,
            latency_micros: 3000,
            max_queue_size: 100,
            random_latency: false,
        };

        let mut simulator = OrderLatencySimulator::new(config);
        let current_timestamp = 1000000;

        // 添加多个订单，时间戳不同
        simulator
            .add_order(create_test_order("test1"), current_timestamp)
            .unwrap();
        simulator
            .add_order(create_test_order("test2"), current_timestamp + 1000)
            .unwrap();
        simulator
            .add_order(create_test_order("test3"), current_timestamp + 2000)
            .unwrap();

        assert_eq!(simulator.queue_size(), 3);

        // 在第一个订单的执行时间，只有第一个订单可执行
        let ready_orders = simulator.get_ready_orders(current_timestamp + 3000);
        assert_eq!(ready_orders.len(), 1);
        assert_eq!(ready_orders[0].id, "test1");

        // 在第二个订单的执行时间，第二个订单可执行
        let ready_orders = simulator.get_ready_orders(current_timestamp + 4000);
        assert_eq!(ready_orders.len(), 1);
        assert_eq!(ready_orders[0].id, "test2");

        // 在第三个订单的执行时间，第三个订单可执行
        let ready_orders = simulator.get_ready_orders(current_timestamp + 5000);
        assert_eq!(ready_orders.len(), 1);
        assert_eq!(ready_orders[0].id, "test3");

        assert_eq!(simulator.queue_size(), 0);
    }

    #[test]
    fn test_order_latency_simulator_random_latency() {
        let config = OrderLatencyConfig {
            enabled: true,
            latency_micros: 1000,
            max_queue_size: 100,
            random_latency: true,
        };

        let mut simulator = OrderLatencySimulator::new(config);
        let current_timestamp = 1000000;

        // 添加多个订单测试随机延迟
        for i in 0..10 {
            simulator
                .add_order(create_test_order(&format!("test{}", i)), current_timestamp)
                .unwrap();
        }

        assert_eq!(simulator.queue_size(), 10);

        // 检查下一个执行时间戳是否在合理范围内 (800-1200微秒延迟)
        if let Some(next_timestamp) = simulator.next_execution_timestamp() {
            let delay = next_timestamp - current_timestamp;
            assert!(
                delay >= 800 && delay <= 1200,
                "Random delay {} should be between 800-1200",
                delay
            );
        }
    }

    #[test]
    fn test_order_latency_simulator_stats() {
        let config = OrderLatencyConfig {
            enabled: true,
            latency_micros: 5000,
            max_queue_size: 100,
            random_latency: false,
        };

        let mut simulator = OrderLatencySimulator::new(config);
        let current_timestamp = 1000000;

        // 添加一些订单
        simulator
            .add_order(create_test_order("test1"), current_timestamp)
            .unwrap();
        simulator
            .add_order(create_test_order("test2"), current_timestamp + 1000)
            .unwrap();

        let stats = simulator.get_stats();
        assert_eq!(stats.queue_size, 2);
        assert!(stats.enabled);
        assert_eq!(stats.latency_micros, 5000);
        assert!(!stats.random_latency);
        assert!(stats.min_execution_timestamp.is_some());
        assert!(stats.max_execution_timestamp.is_some());
    }
}
