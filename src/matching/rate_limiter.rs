use crate::config::PlaybackConfig;
use tokio::time::{sleep, Duration, Instant};
use tracing::{debug, info};

/// 回放速率控制器
/// 负责控制数据处理的速率，避免过快处理历史数据
#[derive(Debug)]
pub struct RateLimiter {
    /// 回放配置
    config: PlaybackConfig,
    /// 当前秒的开始时间
    current_second_start: Option<Instant>,
    /// 当前秒内已处理的数据量
    processed_in_current_second: u32,
}

impl RateLimiter {
    /// 创建新的速率控制器
    pub fn new(config: PlaybackConfig) -> Self {
        Self {
            config,
            current_second_start: None,
            processed_in_current_second: 0,
        }
    }

    /// 应用速率控制
    /// 如果超过了配置的速率限制，会等待直到下一秒
    pub async fn apply_rate_control(&mut self) -> crate::Result<()> {
        if !self.config.enabled || self.config.rate_per_second == 0 {
            // 如果未启用速率控制或速率为0（无限制），直接返回
            debug!(
                "Rate control disabled or unlimited (enabled={}, rate={})",
                self.config.enabled, self.config.rate_per_second
            );
            return Ok(());
        }

        let now = Instant::now();

        // 初始化或重置计数器
        if let Some(second_start) = self.current_second_start {
            if now.duration_since(second_start) >= Duration::from_secs(1) {
                // 新的一秒开始，重置计数器
                self.current_second_start = Some(now);
                self.processed_in_current_second = 0;
            }
        } else {
            // 第一次运行，初始化
            self.current_second_start = Some(now);
            self.processed_in_current_second = 0;
        }

        // 检查是否超过了当前秒的处理限制
        debug!(
            "Rate control check: processed={}, limit={}",
            self.processed_in_current_second, self.config.rate_per_second
        );

        if self.processed_in_current_second >= self.config.rate_per_second {
            // 计算需要等待的时间
            let second_start = self.current_second_start.unwrap();
            let elapsed = now.duration_since(second_start);
            if elapsed < Duration::from_secs(1) {
                let wait_time = Duration::from_secs(1) - elapsed;
                debug!(
                    "🕒 Rate limit reached, waiting {:?} processed={}, limit={}",
                    wait_time, self.processed_in_current_second, self.config.rate_per_second
                );
                sleep(wait_time).await;
                self.current_second_start = Some(Instant::now());
                self.processed_in_current_second = 0;
                debug!("⏱️ Rate control reset after waiting");
            } else {
                debug!("Second already elapsed, no need to wait");
            }
        }

        Ok(())
    }

    /// 记录处理了一个数据项
    pub fn record_processed(&mut self) {
        self.processed_in_current_second += 1;
    }

    /// 批量记录处理的数据项
    pub fn record_batch_processed(&mut self, count: u32) {
        self.processed_in_current_second += count;
    }

    /// 更新配置
    pub fn update_config(&mut self, config: PlaybackConfig) {
        info!(
            "Updating rate limiter config: rate_per_second={}, enabled={}, batch_size={}",
            config.rate_per_second, config.enabled, config.batch_size
        );
        self.config = config;
        // 重置速率控制状态
        self.current_second_start = None;
        self.processed_in_current_second = 0;
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &PlaybackConfig {
        &self.config
    }

    /// 获取当前秒内已处理的数据量
    pub fn get_processed_count(&self) -> u32 {
        self.processed_in_current_second
    }

    /// 检查是否启用了速率控制
    pub fn is_enabled(&self) -> bool {
        self.config.enabled && self.config.rate_per_second > 0
    }

    /// 重置速率控制状态
    pub fn reset(&mut self) {
        self.current_second_start = None;
        self.processed_in_current_second = 0;
    }

    /// 获取批处理大小
    pub fn get_batch_size(&self) -> u32 {
        self.config.batch_size
    }

    /// 计算当前速率（每秒处理的数据量）
    pub fn calculate_current_rate(&self) -> f64 {
        if let Some(second_start) = self.current_second_start {
            let elapsed = second_start.elapsed();
            if elapsed.as_secs_f64() > 0.0 {
                self.processed_in_current_second as f64 / elapsed.as_secs_f64()
            } else {
                0.0
            }
        } else {
            0.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::TimeAlignmentConfig;

    #[tokio::test]
    async fn test_rate_limiter_disabled() {
        let config = PlaybackConfig {
            rate_per_second: 100,
            enabled: false,
            batch_size: 10,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        let mut limiter = RateLimiter::new(config);

        // 禁用时应该立即返回
        let start = Instant::now();
        limiter.apply_rate_control().await.unwrap();
        let elapsed = start.elapsed();

        assert!(elapsed.as_millis() < 10); // 应该几乎立即返回
    }

    #[tokio::test]
    async fn test_rate_limiter_unlimited() {
        let config = PlaybackConfig {
            rate_per_second: 0, // 0 表示无限制
            enabled: true,
            batch_size: 10,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        let mut limiter = RateLimiter::new(config);

        // 无限制时应该立即返回
        let start = Instant::now();
        limiter.apply_rate_control().await.unwrap();
        let elapsed = start.elapsed();

        assert!(elapsed.as_millis() < 10); // 应该几乎立即返回
    }

    #[test]
    fn test_record_processed() {
        let config = PlaybackConfig {
            rate_per_second: 100,
            enabled: true,
            batch_size: 10,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        let mut limiter = RateLimiter::new(config);

        assert_eq!(limiter.get_processed_count(), 0);

        limiter.record_processed();
        assert_eq!(limiter.get_processed_count(), 1);

        limiter.record_batch_processed(5);
        assert_eq!(limiter.get_processed_count(), 6);
    }

    #[test]
    fn test_config_update() {
        let initial_config = PlaybackConfig {
            rate_per_second: 100,
            enabled: true,
            batch_size: 10,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        let mut limiter = RateLimiter::new(initial_config);

        let new_config = PlaybackConfig {
            rate_per_second: 200,
            enabled: false,
            batch_size: 20,
            time_alignment: TimeAlignmentConfig::default(),
            order_latency: crate::config::OrderLatencyConfig::default(),
        };

        limiter.update_config(new_config.clone());

        assert_eq!(limiter.get_config().rate_per_second, 200);
        assert!(!limiter.get_config().enabled);
        assert_eq!(limiter.get_config().batch_size, 20);
    }
}
