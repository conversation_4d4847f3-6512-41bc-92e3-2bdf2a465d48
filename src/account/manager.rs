use crate::account::account::{Account, AccountSummary};
use crate::account::types::{AccountConfig, TradeRecord};
use crate::types::{Price, Trade};
use crate::{BacktestError, Result};
use chrono::Utc;
use std::collections::HashMap;

/// 账户管理器
pub struct AccountManager {
    /// 账户实例
    account: Account,
    /// 当前市场价格
    current_prices: HashMap<String, Price>,
}

impl AccountManager {
    /// 创建新的账户管理器
    pub fn new(account_id: String, config: AccountConfig) -> Self {
        let account = Account::new(account_id, config);

        Self {
            account,
            current_prices: HashMap::new(),
        }
    }

    /// 从trade.id中提取order_id的辅助函数（用于测试）
    fn extract_order_id_from_trade_id(trade_id: &str) -> String {
        if trade_id.contains('_') {
            let parts: Vec<&str> = trade_id.split('_').collect();
            if parts.len() >= 2 {
                // 取第一部分作为order_id，忽略时间戳部分
                parts[0].to_string()
            } else {
                trade_id.to_string()
            }
        } else {
            trade_id.to_string()
        }
    }

    /// 处理交易（带预计算的手续费和maker/taker信息）
    pub fn process_trade(&mut self, trade: Trade, commission: f64, is_maker: bool) -> Result<()> {
        // 使用Trade中的时间戳，如果没有则使用当前时间
        let timestamp = trade.timestamp.unwrap_or_else(|| Utc::now());

        // 从trade.id中提取order_id
        // trade.id格式为: "order_id_timestamp"
        let order_id = Self::extract_order_id_from_trade_id(&trade.id);

        let trade_record = TradeRecord::new_with_timestamp(
            trade.id.clone(),
            order_id,             // 使用正确的订单ID
            trade.symbol.clone(), // 从trade中获取交易对
            trade.side,
            trade.price,
            trade.quantity,
            commission, // 使用预计算的手续费
            "USDT".to_string(),
            is_maker,
            timestamp, // 使用Trade中的时间戳
        );

        // 记录交易到回测记录器
        self.record_trade(&trade_record);

        self.account
            .process_trade(trade_record, &self.current_prices)
            .map_err(|e| BacktestError::Account(e))?;

        Ok(())
    }

    /// 记录交易到回测记录器
    fn record_trade(&self, trade_record: &TradeRecord) {
        use crate::state::get_backtest_recorder;
        use tokio::task::spawn_blocking;

        // 异步记录交易
        let trade_record = trade_record.clone();
        spawn_blocking(move || {
            tokio::runtime::Handle::current().block_on(async {
                if let Some(recorder) = get_backtest_recorder().await {
                    let mut recorder = recorder.lock().await;
                    recorder.record_trade(&trade_record);
                }
            });
        });
    }

    /// 更新市场价格
    pub fn update_price(&mut self, symbol: String, price: Price) {
        self.current_prices.insert(symbol, price);
    }

    /// 获取账户摘要
    pub fn get_account_summary(&self) -> AccountSummary {
        self.account.get_summary(&self.current_prices)
    }

    /// 获取账户可用余额
    pub fn get_balance(&self, asset: &str) -> f64 {
        self.account.balance_manager.get_available_balance(asset)
    }

    /// 获取账户总余额
    pub fn get_total_balance(&self, asset: &str) -> f64 {
        self.account.balance_manager.get_total_balance(asset)
    }

    /// 获取仓位信息
    pub fn get_position(&self, symbol: &str) -> Option<&crate::account::position::Position> {
        self.account.get_position(symbol)
    }

    /// 获取账户净值
    pub fn get_net_value(&self) -> f64 {
        self.account.calculate_net_value(&self.current_prices)
    }

    /// 冻结保证金
    pub fn freeze_margin(&mut self, asset: &str, amount: f64) -> std::result::Result<(), String> {
        self.account.balance_manager.freeze_balance(asset, amount)
    }

    /// 解冻保证金
    pub fn unfreeze_margin(&mut self, asset: &str, amount: f64) -> std::result::Result<(), String> {
        self.account.balance_manager.unfreeze_balance(asset, amount)
    }

    /// 获取最大杠杆
    pub fn get_max_leverage(&self) -> f64 {
        self.account.config.max_leverage
    }

    /// 获取maker手续费率
    pub fn get_maker_fee_rate(&self) -> f64 {
        self.account.config.maker_fee_rate
    }

    /// 获取taker手续费率
    pub fn get_taker_fee_rate(&self) -> f64 {
        self.account.config.taker_fee_rate
    }

    /// 验证账户状态
    pub fn validate_account(&self) -> Result<()> {
        self.account
            .validate()
            .map_err(|e| BacktestError::Account(e))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::OrderSide;

    #[test]
    fn test_account_manager_creation() {
        let config = AccountConfig::default();
        let manager = AccountManager::new("test_account".to_string(), config.clone());

        let summary = manager.get_account_summary();
        assert_eq!(summary.account_id, "test_account");
        assert_eq!(summary.stats.available_balance, config.initial_balance);
    }

    #[test]
    fn test_order_id_extraction() {
        // Test order_id extraction from trade.id
        let trade_id = "order_123_1640995200000000";
        let order_id = AccountManager::extract_order_id_from_trade_id(trade_id);
        assert_eq!(order_id, "order");

        // Test with different format
        let trade_id = "test_order_456_1640995200000000";
        let order_id = AccountManager::extract_order_id_from_trade_id(trade_id);
        assert_eq!(order_id, "test");

        // Test with no underscore
        let trade_id = "simple_trade_id";
        let order_id = AccountManager::extract_order_id_from_trade_id(trade_id);
        assert_eq!(order_id, "simple");

        // Test with single underscore
        let trade_id = "order_123";
        let order_id = AccountManager::extract_order_id_from_trade_id(trade_id);
        assert_eq!(order_id, "order");
    }

    #[test]
    fn test_maker_taker_fee_calculation() {
        let config = AccountConfig::default();

        // 测试taker手续费 (0.04%)
        let taker_commission = 1.0 * 50000.0 * config.taker_fee_rate;
        assert_eq!(taker_commission, 20.0); // 50000 * 0.0004 = 20

        // 测试maker手续费 (0.02%)
        let maker_commission = 1.0 * 50000.0 * config.maker_fee_rate;
        assert_eq!(maker_commission, 10.0); // 50000 * 0.0002 = 10

        // 验证费率获取方法
        let manager = AccountManager::new("test_account".to_string(), config.clone());
        assert_eq!(manager.get_taker_fee_rate(), 0.0004);
        assert_eq!(manager.get_maker_fee_rate(), 0.0002);

        // 测试taker交易
        let mut manager_taker = AccountManager::new("test_taker".to_string(), config.clone());
        let trade_taker = Trade {
            id: "test_trade_taker".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(50000.0),
            quantity: 0.1, // 使用较小的数量避免保证金不足
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
            data_source_type: crate::config::DataSourceType::BinanceTardis,
        };

        let result_taker = manager_taker.process_trade(trade_taker, 2.0, false); // 0.1 * 50000 * 0.0004 = 2.0
        assert!(result_taker.is_ok());

        // 测试maker交易
        let mut manager_maker = AccountManager::new("test_maker".to_string(), config.clone());
        let trade_maker = Trade {
            id: "test_trade_maker".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(50000.0),
            quantity: 0.1, // 使用较小的数量避免保证金不足
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
            data_source_type: crate::config::DataSourceType::BinanceTardis,
        };

        let result_maker = manager_maker.process_trade(trade_maker, 1.0, true); // 0.1 * 50000 * 0.0002 = 1.0
        assert!(result_maker.is_ok());
    }

    #[test]
    fn test_account_manager_process_trade() {
        let config = AccountConfig::default();
        let mut manager = AccountManager::new("test_account".to_string(), config);

        // 更新价格
        manager.update_price("BTCUSDT".to_string(), Price::new(50000.0));

        // 创建交易
        let trade = Trade {
            id: "trade1".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(50000.0),
            quantity: 0.1,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
            data_source_type: crate::config::DataSourceType::BinanceOfficial,
        };

        // 计算手续费 (使用taker费率作为示例)
        let commission = 0.1 * 50000.0 * 0.0004; // quantity * price * taker_fee_rate
        let result = manager.process_trade(trade, commission, false);
        assert!(result.is_ok());

        // 检查余额变化
        let balance = manager.get_balance("USDT");
        assert!(balance < 10000.0); // 余额应该减少

        // 检查仓位
        let position = manager.get_position("BTCUSDT");
        assert!(position.is_some());
        let pos = position.unwrap();
        assert_eq!(pos.quantity, 0.1);
    }

    #[test]
    fn test_account_manager_price_update() {
        let config = AccountConfig::default();
        let mut manager = AccountManager::new("test_account".to_string(), config);

        // 更新价格
        manager.update_price("BTCUSDT".to_string(), Price::new(50000.0));
        manager.update_price("ETHUSDT".to_string(), Price::new(3000.0));

        // 价格应该被正确存储
        assert_eq!(
            manager.current_prices.get("BTCUSDT").unwrap().value(),
            50000.0
        );
        assert_eq!(
            manager.current_prices.get("ETHUSDT").unwrap().value(),
            3000.0
        );
    }

    #[test]
    fn test_account_manager_net_value() {
        let config = AccountConfig::default();
        let mut manager = AccountManager::new("test_account".to_string(), config);

        // 更新价格
        manager.update_price("BTCUSDT".to_string(), Price::new(50000.0));

        // 处理买入交易
        let trade = Trade {
            id: "trade1".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(50000.0),
            quantity: 0.1,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
            data_source_type: crate::config::DataSourceType::BinanceOfficial,
        };
        // 计算手续费 (使用taker费率作为示例)
        let commission = 0.1 * 50000.0 * 0.0004; // quantity * price * taker_fee_rate
        manager.process_trade(trade, commission, false).unwrap();

        // 价格上涨
        manager.update_price("BTCUSDT".to_string(), Price::new(55000.0));

        let net_value = manager.get_net_value();

        // 计算预期净值：初始余额 - 交易成本 + 未实现盈亏
        let trade_cost = 50000.0 * 0.1 + 0.1 * 50000.0 * 0.0004; // 成本 + 手续费
        let unrealized_pnl = 0.1 * (55000.0 - 50000.0); // 未实现盈亏
        let expected_net_value = 10000.0 - trade_cost + unrealized_pnl;

        println!(
            "Net value: {}, Expected: {}, Trade cost: {}, Unrealized PnL: {}",
            net_value, expected_net_value, trade_cost, unrealized_pnl
        );

        // 由于价格上涨，净值应该比初始成本高
        assert!(net_value > (10000.0 - trade_cost)); // 净值应该比扣除交易成本后的余额高
    }

    #[test]
    fn test_account_manager_validation() {
        let config = AccountConfig::default();
        let manager = AccountManager::new("test_account".to_string(), config);

        let result = manager.validate_account();
        assert!(result.is_ok());
    }

    #[test]
    fn test_account_manager_multiple_trades() {
        let config = AccountConfig::default();
        let mut manager = AccountManager::new("test_account".to_string(), config);

        // 更新价格
        manager.update_price("BTCUSDT".to_string(), Price::new(50000.0));

        // 第一笔交易：买入
        let trade1 = Trade {
            id: "trade1".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(50000.0),
            quantity: 0.1,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
            data_source_type: crate::config::DataSourceType::BinanceOfficial,
        };
        // 计算第一笔交易手续费
        let commission1 = 0.1 * 50000.0 * 0.0004; // quantity * price * taker_fee_rate
        manager.process_trade(trade1, commission1, false).unwrap();

        // 第二笔交易：再次买入（加仓）
        let trade2 = Trade {
            id: "trade2".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(52000.0),
            quantity: 0.05,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
            data_source_type: crate::config::DataSourceType::BinanceOfficial,
        };
        // 计算第二笔交易手续费
        let commission2 = 0.05 * 50000.0 * 0.0004; // quantity * price * taker_fee_rate
        manager.process_trade(trade2, commission2, false).unwrap();

        // 检查仓位
        let position = manager.get_position("BTCUSDT").unwrap();
        assert!((position.quantity - 0.15).abs() < 1e-10); // 总持仓应该是0.15

        // 检查平均价格
        let expected_avg_price = (50000.0 * 0.1 + 52000.0 * 0.05) / 0.15;
        assert!((position.avg_price.value() - expected_avg_price).abs() < 1e-6);
    }
}
