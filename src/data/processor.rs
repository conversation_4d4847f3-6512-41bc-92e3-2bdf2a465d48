use crate::types::{MarketData, TimeBarrier};
use crate::{BacktestError, Result};
use tokio::sync::{broadcast, mpsc};
use tracing::{error, info};

/// 数据处理器
/// 负责处理时间屏障和数据同步
pub struct DataProcessor {
    input_rx: mpsc::Receiver<MarketData>,
    output_tx: broadcast::Sender<MarketData>,
    current_barrier: Option<TimeBarrier>,
    /// 数据缓冲窗口（微秒），允许在这个时间窗口内的数据通过
    buffer_window_micros: u64,
    /// 数据缓冲窗口（update_id），允许在这个update_id范围内的数据通过
    buffer_window_update_id: u64,
}

impl DataProcessor {
    /// 创建新的数据处理器
    pub fn new(
        input_rx: mpsc::Receiver<MarketData>,
        output_tx: broadcast::Sender<MarketData>,
    ) -> Self {
        Self {
            input_rx,
            output_tx,
            current_barrier: None,
            buffer_window_micros: 1_000_000, // 1秒缓冲窗口
            buffer_window_update_id: 100,    // 100个update_id缓冲窗口
        }
    }

    /// 开始处理数据
    pub async fn start_processing(&mut self) -> Result<()> {
        info!("Starting data processing");

        while let Some(market_data) = self.input_rx.recv().await {
            match self.process_market_data(market_data).await {
                Ok(()) => {}
                Err(e) => {
                    error!("Failed to process market data: {}", e);
                    continue;
                }
            }
        }

        info!("Data processing completed");
        Ok(())
    }

    /// 处理单个市场数据
    async fn process_market_data(&mut self, market_data: MarketData) -> Result<()> {
        if let Err(e) = self.output_tx.send(market_data) {
            error!("Failed to broadcast market data: {}", e);
            return Err(BacktestError::Communication(format!(
                "Failed to broadcast data: {}",
                e
            )));
        }

        Ok(())
    }

    /// 手动设置时间屏障
    pub fn set_time_barrier(&mut self, barrier: TimeBarrier) {
        self.current_barrier = Some(barrier);
    }

    /// 获取当前时间屏障
    pub fn get_current_barrier(&self) -> Option<&TimeBarrier> {
        self.current_barrier.as_ref()
    }

    /// 重置时间屏障
    pub fn reset_barrier(&mut self) {
        self.current_barrier = None;
    }

    /// 设置时间缓冲窗口（微秒）
    pub fn set_time_buffer_window(&mut self, window_micros: u64) {
        self.buffer_window_micros = window_micros;
    }

    /// 设置update_id缓冲窗口
    pub fn set_update_id_buffer_window(&mut self, window: u64) {
        self.buffer_window_update_id = window;
    }

    /// 获取当前缓冲窗口配置
    pub fn get_buffer_config(&self) -> (u64, u64) {
        (self.buffer_window_micros, self.buffer_window_update_id)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{Bbo, Price};

    #[tokio::test]
    async fn test_data_processor() {
        let (input_tx, input_rx) = mpsc::channel(100);
        let (output_tx, _output_rx) = broadcast::channel(100);

        let mut processor = DataProcessor::new(input_rx, output_tx);

        // 测试BBO数据处理
        let bbo = Bbo {
            update_id: 12345,
            bid_price: Price::new(99.5),
            bid_quantity: 10.0,
            ask_price: Price::new(100.5),
            ask_quantity: 15.0,
            timestamp: Some(1640995200000000), // 微秒级时间戳
            data_source_type: crate::config::DataSourceType::BinanceOfficial,
        };

        let market_data = MarketData::Bbo(bbo);

        // 发送测试数据
        input_tx.send(market_data).await.unwrap();
        drop(input_tx);

        // 处理数据
        processor.start_processing().await.unwrap();
    }
}
