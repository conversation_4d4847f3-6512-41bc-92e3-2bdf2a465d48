use crate::config::DataSourceType;
use crate::types::{Bbo, MarketData, OrderBookSnapshot, OrderSide, Price, TradeData};
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use std::collections::BTreeMap;
use tracing::debug;

/// 数据解析器
/// 负责解析不同格式的市场数据
pub struct DataParser;

impl DataParser {
    /// 从文件路径提取symbol（用于Binance格式）
    pub fn extract_symbol_from_filename(file_path: &str) -> Option<String> {
        if let Some(filename) = std::path::Path::new(file_path).file_name() {
            if let Some(filename_str) = filename.to_str() {
                // 处理格式如: 2025-07-07_BTCUSDT.csv.gz
                if let Some(symbol_part) = filename_str.split('_').nth(1) {
                    if let Some(symbol) = symbol_part.split('.').next() {
                        return Some(symbol.to_string());
                    }
                }
            }
        }
        None
    }

    /// 从CSV数据行提取symbol（用于OKX格式）
    pub fn extract_symbol_from_csv_line(line: &str) -> Option<String> {
        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() >= 2 {
            // symbol在第二列（索引1）
            return Some(parts[1].to_string());
        }
        None
    }

    /// 解析 quotes CSV 行数据
    pub fn parse_quotes_csv_line(
        line: &str,
        data_source_type: DataSourceType,
    ) -> Result<Option<MarketData>> {
        if line.trim().is_empty() || line.starts_with("exchange") {
            return Ok(None);
        }

        match data_source_type {
            DataSourceType::BinanceTardis => Self::parse_binance_quotes_line(line),
            DataSourceType::OkxTardis => Self::parse_okx_quotes_line(line),
            DataSourceType::BinanceOfficial => Self::parse_binance_official_quotes_line(line),
        }
    }

    /// 解析 quotes CSV 行数据（带文件路径用于symbol提取）
    pub fn parse_quotes_csv_line_with_path(
        line: &str,
        data_source_type: DataSourceType,
        file_path: Option<&str>,
    ) -> Result<Option<MarketData>> {
        if line.trim().is_empty() || line.starts_with("exchange") {
            return Ok(None);
        }

        match data_source_type {
            DataSourceType::BinanceTardis => {
                Self::parse_binance_quotes_line_with_path(line, file_path)
            }
            DataSourceType::OkxTardis => Self::parse_okx_quotes_line_with_path(line, file_path),
            DataSourceType::BinanceOfficial => Self::parse_binance_official_quotes_line(line),
        }
    }

    /// 解析 trades CSV 行数据
    pub fn parse_trades_csv_line(
        line: &str,
        data_source_type: DataSourceType,
    ) -> Result<Option<MarketData>> {
        if line.trim().is_empty() || line.starts_with("exchange") {
            return Ok(None);
        }

        match data_source_type {
            DataSourceType::BinanceTardis => Self::parse_binance_trades_line(line),
            DataSourceType::OkxTardis => Self::parse_okx_trades_line(line),
            DataSourceType::BinanceOfficial => Self::parse_binance_official_trades_line(line),
        }
    }

    /// 解析 trades CSV 行数据（带文件路径用于symbol提取）
    pub fn parse_trades_csv_line_with_path(
        line: &str,
        data_source_type: DataSourceType,
        file_path: Option<&str>,
    ) -> Result<Option<MarketData>> {
        if line.trim().is_empty() || line.starts_with("exchange") {
            return Ok(None);
        }

        match data_source_type {
            DataSourceType::BinanceTardis => {
                Self::parse_binance_trades_line_with_path(line, file_path)
            }
            DataSourceType::OkxTardis => Self::parse_okx_trades_line_with_path(line, file_path),
            DataSourceType::BinanceOfficial => Self::parse_binance_official_trades_line(line),
        }
    }

    /// 解析 depth CSV 行数据
    pub async fn parse_depth_csv_line(
        line: &str,
        data_source_type: DataSourceType,
    ) -> Result<Option<MarketData>> {
        if line.trim().is_empty() || line.starts_with("exchange") {
            return Ok(None);
        }

        match data_source_type {
            DataSourceType::BinanceTardis => Self::parse_binance_depth_line(line).await,
            DataSourceType::OkxTardis => {
                // OKX depth 解析可以后续实现
                Ok(None)
            }
            DataSourceType::BinanceOfficial => {
                // Binance Official depth 解析可以后续实现
                Ok(None)
            }
        }
    }

    /// 解析 depth CSV 行数据（带文件路径用于symbol提取）
    pub async fn parse_depth_csv_line_with_path(
        line: &str,
        data_source_type: DataSourceType,
        file_path: Option<&str>,
    ) -> Result<Option<MarketData>> {
        if line.trim().is_empty() || line.starts_with("exchange") {
            return Ok(None);
        }

        match data_source_type {
            DataSourceType::BinanceTardis => {
                Self::parse_binance_depth_line_with_path(line, file_path).await
            }
            DataSourceType::OkxTardis => {
                // OKX depth 解析可以后续实现
                Ok(None)
            }
            DataSourceType::BinanceOfficial => {
                // Binance Official depth 解析可以后续实现
                Ok(None)
            }
        }
    }

    /// 检查数据是否在时间范围内
    pub fn is_data_in_time_range(
        market_data: &MarketData,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> bool {
        match market_data {
            MarketData::Bbo(bbo) => {
                if let Some(timestamp) = bbo.timestamp {
                    Self::is_timestamp_in_range(timestamp, start_time, end_time)
                } else {
                    true
                }
            }
            MarketData::TradeData(trade_data) => {
                Self::is_timestamp_in_range(trade_data.timestamp, start_time, end_time)
            }
            _ => true,
        }
    }

    /// 检查时间戳是否在指定范围内
    fn is_timestamp_in_range(
        timestamp_micros: u64,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> bool {
        let start_micros = start_time.timestamp_micros() as u64;
        let end_micros = end_time.timestamp_micros() as u64;
        timestamp_micros >= start_micros && timestamp_micros <= end_micros
    }

    /// 解析 Binance Tardis quotes 行
    fn parse_binance_quotes_line(line: &str) -> Result<Option<MarketData>> {
        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() < 8 {
            return Err(BacktestError::Data(format!(
                "Invalid Binance quotes line format: expected 8 fields, got {}",
                parts.len()
            )));
        }

        // 格式：exchange,symbol,timestamp,local_timestamp,ask_amount,ask_price,bid_price,bid_amount
        let timestamp = parts[2]
            .parse::<u64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse timestamp: {}", e)))?;

        let ask_quantity = parts[4]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse ask quantity: {}", e)))?;

        let ask_price = parts[5]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse ask price: {}", e)))?;

        let bid_price = parts[6]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse bid price: {}", e)))?;

        let bid_quantity = parts[7]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse bid quantity: {}", e)))?;

        let bbo = Bbo {
            update_id: 0,
            bid_price: Price(bid_price),
            bid_quantity,
            ask_price: Price(ask_price),
            ask_quantity,
            timestamp: Some(timestamp),
            data_source_type: DataSourceType::BinanceTardis,
        };

        Ok(Some(MarketData::Bbo(bbo)))
    }

    /// 解析 Binance Tardis quotes 行（带文件路径用于symbol提取）
    fn parse_binance_quotes_line_with_path(
        line: &str,
        _file_path: Option<&str>,
    ) -> Result<Option<MarketData>> {
        // 对于Binance，我们不需要从CSV中提取symbol，因为文件名包含symbol
        // 这里保持与原函数相同的逻辑
        Self::parse_binance_quotes_line(line)
    }

    /// 解析 OKX Tardis quotes 行
    fn parse_okx_quotes_line(line: &str) -> Result<Option<MarketData>> {
        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() < 8 {
            return Err(BacktestError::Data(format!(
                "Invalid OKX quotes line format: expected 8 fields, got {}",
                parts.len()
            )));
        }

        // 格式：exchange,symbol,timestamp,local_timestamp,ask_amount,ask_price,bid_price,bid_amount
        let timestamp = parts[2]
            .parse::<u64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse timestamp: {}", e)))?;

        let ask_quantity = parts[4]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse ask quantity: {}", e)))?;

        let ask_price = parts[5]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse ask price: {}", e)))?;

        let bid_price = parts[6]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse bid price: {}", e)))?;

        let bid_quantity = parts[7]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse bid quantity: {}", e)))?;

        let bbo = Bbo {
            update_id: 0,
            bid_price: Price(bid_price),
            bid_quantity,
            ask_price: Price(ask_price),
            ask_quantity,
            timestamp: Some(timestamp),
            data_source_type: DataSourceType::OkxTardis,
        };

        Ok(Some(MarketData::Bbo(bbo)))
    }

    /// 解析 OKX Tardis quotes 行（带文件路径用于symbol提取）
    fn parse_okx_quotes_line_with_path(
        line: &str,
        _file_path: Option<&str>,
    ) -> Result<Option<MarketData>> {
        // 对于OKX，symbol在CSV数据中，所以我们不需要文件路径
        // 这里保持与原函数相同的逻辑
        Self::parse_okx_quotes_line(line)
    }

    /// 解析 Binance Official quotes 行
    fn parse_binance_official_quotes_line(_line: &str) -> Result<Option<MarketData>> {
        // TODO: 实现 Binance Official 格式解析
        debug!("Binance Official quotes parsing not implemented yet");
        Ok(None)
    }

    /// 解析 Binance Tardis depth 行
    async fn parse_binance_depth_line(line: &str) -> Result<Option<MarketData>> {
        let parts: Vec<&str> = line.split(',').collect();

        debug!("Parsing depth line with {} parts", parts.len());

        // Binance depth snapshot 格式有很多列：
        // exchange,symbol,timestamp,local_timestamp,asks[0].price,asks[0].amount,bids[0].price,bids[0].amount,...
        // 至少需要前8列（包含第一层买卖价格和数量）
        if parts.len() < 8 {
            return Err(BacktestError::Data(format!(
                "Invalid Binance depth line format: expected at least 8 fields, got {}",
                parts.len()
            )));
        }

        // 解析基本信息
        let timestamp = parts[2]
            .parse::<u64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse timestamp: {}", e)))?;

        let _symbol = parts[1].to_string();

        // 解析订单簿数据
        let mut bids = BTreeMap::new();
        let mut asks = BTreeMap::new();

        // 计算有多少层深度数据（每层需要4列：asks[i].price, asks[i].amount, bids[i].price, bids[i].amount）
        let depth_levels = (parts.len() - 4) / 4; // 减去前4列（exchange,symbol,timestamp,local_timestamp）

        for i in 0..depth_levels {
            let base_idx = 4 + i * 4; // 从第5列开始，每4列一组

            if base_idx + 3 < parts.len() {
                // Binance depth格式：asks[i].price, asks[i].amount, bids[i].price, bids[i].amount
                // 解析ask价格和数量
                if let (Ok(ask_price), Ok(ask_amount)) = (
                    parts[base_idx].parse::<f64>(),
                    parts[base_idx + 1].parse::<f64>(),
                ) {
                    if ask_amount > 0.0 {
                        asks.insert(Price::new(ask_price), ask_amount);
                    }
                }

                // 解析bid价格和数量
                if let (Ok(bid_price), Ok(bid_amount)) = (
                    parts[base_idx + 2].parse::<f64>(),
                    parts[base_idx + 3].parse::<f64>(),
                ) {
                    if bid_amount > 0.0 {
                        bids.insert(Price::new(bid_price), bid_amount);
                    }
                }
            }
        }

        // 创建OrderBookSnapshot
        // 使用全局连续的update_id计数器
        let update_id = crate::state::get_next_update_id().await;
        let orderbook = OrderBookSnapshot {
            timestamp,
            update_id,
            bids,
            asks,
        };

        debug!(
            "Parsed OrderBook: {} bids, {} asks, timestamp: {}",
            orderbook.bids.len(),
            orderbook.asks.len(),
            orderbook.timestamp
        );

        Ok(Some(MarketData::OrderBook(orderbook)))
    }

    /// 解析 Binance Tardis depth 行（带文件路径用于symbol提取）
    async fn parse_binance_depth_line_with_path(
        line: &str,
        file_path: Option<&str>,
    ) -> Result<Option<MarketData>> {
        // 对于Binance depth，symbol在CSV数据中，但我们也可以从文件路径中提取作为备用
        let result = Self::parse_binance_depth_line(line).await?;
        debug!("parse depth result: {:?}", result);

        // 如果解析成功且提供了文件路径，可以验证或补充symbol信息
        if let (Some(MarketData::OrderBook(_orderbook)), Some(path)) = (&result, file_path) {
            if let Some(symbol_from_path) = Self::extract_symbol_from_filename(path) {
                debug!("Extracted symbol from path: {}", symbol_from_path);
                // 这里可以添加symbol验证逻辑
            }
        }

        Ok(result)
    }

    /// 解析 Binance Tardis trades 行
    fn parse_binance_trades_line(line: &str) -> Result<Option<MarketData>> {
        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() < 8 {
            return Err(BacktestError::Data(format!(
                "Invalid Binance trades line format: expected 8 fields, got {}",
                parts.len()
            )));
        }

        // 格式：exchange,symbol,timestamp,local_timestamp,id,side,price,amount
        let timestamp = parts[2]
            .parse::<u64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse timestamp: {}", e)))?;

        let price = parts[6]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse price: {}", e)))?;

        let quantity = parts[7]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse quantity: {}", e)))?;

        let side = parts[5];
        let is_buyer_maker = side == "sell"; // 如果是卖单，则买方是 maker

        // 从CSV数据中提取symbol（第二列）
        let symbol = parts[1].to_string();

        let trade_data = TradeData {
            exchange: "binance".to_string(),
            symbol,
            timestamp,
            local_timestamp: timestamp,
            id: "0".to_string(),
            side: if is_buyer_maker {
                OrderSide::Sell
            } else {
                OrderSide::Buy
            },
            price: Price(price),
            amount: quantity,
            data_source_type: DataSourceType::BinanceTardis,
        };

        Ok(Some(MarketData::TradeData(trade_data)))
    }

    /// 解析 Binance Tardis trades 行（带文件路径用于symbol提取）
    fn parse_binance_trades_line_with_path(
        line: &str,
        _file_path: Option<&str>,
    ) -> Result<Option<MarketData>> {
        // 对于Binance，symbol在CSV数据中，所以我们不需要文件路径
        // 这里保持与原函数相同的逻辑
        Self::parse_binance_trades_line(line)
    }

    /// 解析 OKX Tardis trades 行
    fn parse_okx_trades_line(line: &str) -> Result<Option<MarketData>> {
        let parts: Vec<&str> = line.split(',').collect();
        if parts.len() < 8 {
            return Err(BacktestError::Data(format!(
                "Invalid OKX trades line format: expected 8 fields, got {}",
                parts.len()
            )));
        }

        // 格式：exchange,symbol,timestamp,local_timestamp,id,side,price,amount
        let timestamp = parts[2]
            .parse::<u64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse timestamp: {}", e)))?;

        let price = parts[6]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse price: {}", e)))?;

        let quantity = parts[7]
            .parse::<f64>()
            .map_err(|e| BacktestError::Data(format!("Failed to parse quantity: {}", e)))?;

        let side = parts[5];
        let is_buyer_maker = side == "sell"; // 如果是卖单，则买方是 maker

        // 从CSV数据中提取symbol（第二列）
        let symbol = parts[1].to_string();

        let trade_data = TradeData {
            exchange: "okx".to_string(),
            symbol,
            timestamp,
            local_timestamp: timestamp,
            id: "0".to_string(),
            side: if is_buyer_maker {
                OrderSide::Sell
            } else {
                OrderSide::Buy
            },
            price: Price(price),
            amount: quantity,
            data_source_type: DataSourceType::OkxTardis,
        };

        Ok(Some(MarketData::TradeData(trade_data)))
    }

    /// 解析 OKX Tardis trades 行（带文件路径用于symbol提取）
    fn parse_okx_trades_line_with_path(
        line: &str,
        _file_path: Option<&str>,
    ) -> Result<Option<MarketData>> {
        // 对于OKX，symbol在CSV数据中，所以我们不需要文件路径
        // 这里保持与原函数相同的逻辑
        Self::parse_okx_trades_line(line)
    }

    /// 解析 Binance Official trades 行
    fn parse_binance_official_trades_line(_line: &str) -> Result<Option<MarketData>> {
        // TODO: 实现 Binance Official 格式解析
        debug!("Binance Official trades parsing not implemented yet");
        Ok(None)
    }
}
