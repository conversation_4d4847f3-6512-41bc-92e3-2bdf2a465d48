start_time = "2025-07-23T00:00:00Z"
end_time = "2025-07-23T23:59:59Z"
websocket_port = 8084
http_port = 8085
log_level = "info"
performance_target_us = 500

# HTTP服务器TLS配置
[http_tls]
# 启用TLS（HTTPS）
enabled = false

# 使用证书文件
[http_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

# WebSocket服务器TLS配置
[websocket_tls]
# 启用TLS（WSS）
enabled = false

# 使用证书文件
[websocket_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

# 数据路径配置
[[data_paths]]
data_source_type = "OkxTardis"

[data_paths.sources]
root = "./data/okx/"
trades = "./data/okx/trades"
quotes = "./data/okx/quotes"

[[data_paths]]
data_source_type = "BinanceTardis"

[data_paths.sources]
root = "./data/binance/"
trades = "./data/binance/trades"
quotes = "./data/binance/quotes"
depth = "./data/binance/depth"

[account]
account_type = "Futures"
initial_balance = 10000.0
max_leverage = 10.0
margin_mode = "Cross"
maker_fee_rate = 0.0002
taker_fee_rate = 0.0004
funding_rate = 0.0001
supported_symbols = ["BTCUSDT", "ETHUSDT"]

# 数据回放配置
[playback]
# 回放速率（每秒处理的数据条数）
# 0 表示无限制，尽可能快地处理
rate_per_second = 2
# 是否启用回放速率控制
enabled = true
# 批处理大小（一次处理多少条数据）
batch_size = 10

# 时间对齐配置
[playback.time_alignment]
# 是否启用时间对齐
enabled = true
# 时间窗口大小（微秒）
time_window_micros = 100000
# 缓冲区大小限制
buffer_size_limit = 1000
# 最大等待时间（微秒）
max_wait_time_micros = 1000000
# 时间容差（微秒）
time_tolerance_micros = 10000

# 订单延迟模拟配置
[playback.order_latency]
# 是否启用订单延迟模拟
enabled = false
# 订单延迟时间（微秒）- 默认3ms
latency_micros = 3000
# 延迟队列最大大小
max_queue_size = 10000
# 是否启用随机延迟（在延迟时间的80%-120%范围内随机）
random_latency = false
