use backtest::account::manager::AccountManager;
use backtest::account::types::AccountConfig;
use backtest::config::{DataSourceType, OrderLatencyConfig, PlaybackConfig, TimeAlignmentConfig};
use backtest::matching::MatchingEngine;
use backtest::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price};
use chrono::Utc;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{broadcast, mpsc, Mutex};
use tracing::info;

/// 创建测试用的BBO数据
fn create_test_bbo(timestamp_micros: u64, bid_price: f64, ask_price: f64) -> MarketData {
    MarketData::Bbo(Bbo {
        update_id: 0,
        bid_price: Price::new(bid_price),
        bid_quantity: 100.0,
        ask_price: Price::new(ask_price),
        ask_quantity: 100.0,
        timestamp: Some(timestamp_micros),
        data_source_type: DataSourceType::BinanceTardis,
    })
}

/// 创建测试订单
fn create_test_order(id: &str, side: OrderSide, price: Option<f64>, quantity: f64) -> Order {
    Order {
        id: id.to_string(),
        client_order_id: format!("client_{}", id),
        symbol: "BTCUSDT".to_string(),
        order_type: if price.is_some() {
            OrderType::Limit
        } else {
            OrderType::Market
        },
        side,
        price: price.map(Price::new),
        quantity,
        status: OrderStatus::Pending,
        timestamp: Utc::now(),
        execution_info: None,
    }
}

#[tokio::test]
async fn test_order_latency_debug_basic() {
    tracing_subscriber::fmt::init();
    info!("🔍 开始调试基本订单延迟功能");

    // 配置延迟
    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 3000, // 3ms
        max_queue_size: 1000,
        random_latency: false,
    };

    // 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(1000);
    let (order_tx, order_rx) = mpsc::channel(1000);
    let (trade_tx, mut trade_rx) = broadcast::channel(1000);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(1000);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(1000);

    // 创建账户管理器，设置足够的余额
    let mut account_config = AccountConfig::default();
    account_config.initial_balance = 1000000.0; // 100万USDT
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        account_config,
    )));

    // 创建回放配置
    let playback_config = PlaybackConfig {
        rate_per_second: 0,
        enabled: false,
        batch_size: 1,
        time_alignment: TimeAlignmentConfig::default(),
        order_latency: latency_config,
    };

    // 创建撮合引擎
    let mut matching_engine = MatchingEngine::new_with_playback_config(
        account_manager,
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        playback_config,
    );

    // 检查初始延迟统计
    let initial_stats = matching_engine.get_order_latency_stats();
    info!("📊 初始延迟统计:");
    info!("   启用: {}", initial_stats.enabled);
    info!("   延迟时间: {}微秒", initial_stats.latency_micros);
    info!("   队列大小: {}", initial_stats.queue_size);

    assert!(initial_stats.enabled, "延迟模拟应该被启用");
    assert_eq!(initial_stats.latency_micros, 3000, "延迟时间应该是3ms");

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    // 等待引擎启动
    tokio::time::sleep(Duration::from_millis(100)).await;

    // 发送初始市场数据
    let base_timestamp = 1000000;
    let initial_bbo = create_test_bbo(base_timestamp, 50000.0, 50100.0);
    info!(
        "📈 发送初始BBO: bid={}, ask={}, timestamp={}",
        50000.0, 50100.0, base_timestamp
    );
    market_data_tx.send(initial_bbo).unwrap();

    // 等待市场数据处理
    tokio::time::sleep(Duration::from_millis(20)).await;

    // 提交订单
    let order = create_test_order("debug_order", OrderSide::Buy, Some(50050.0), 1.0);
    info!(
        "📝 提交订单: {} {:?} {} @ {}",
        order.id,
        order.side,
        order.quantity,
        order.price.as_ref().unwrap()
    );
    order_tx.send(order).await.unwrap();

    // 等待订单处理
    tokio::time::sleep(Duration::from_millis(10)).await;

    // 推进市场数据时间以触发延迟订单执行
    for i in 1..=10 {
        let timestamp = base_timestamp + (i * 1000); // 每1ms
        let bbo = create_test_bbo(timestamp, 50000.0, 50100.0);
        info!("📈 推进市场时间: timestamp={} (+{}ms)", timestamp, i);
        market_data_tx.send(bbo).unwrap();

        // 等待处理
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 检查是否有交易或订单更新
        let mut has_trade = false;
        let mut has_order_update = false;

        // 非阻塞检查交易
        if let Ok(trade) = trade_rx.try_recv() {
            has_trade = true;
            info!(
                "✅ 收到交易: {} @ {} 数量: {}",
                trade.id, trade.price, trade.quantity
            );
        }

        // 非阻塞检查订单更新
        if let Ok(order_update) = order_update_rx.try_recv() {
            has_order_update = true;
            info!(
                "📋 收到订单更新: {} 状态: {:?}",
                order_update.id, order_update.status
            );
        }

        if has_trade || has_order_update {
            info!("🎯 在第{}ms检测到活动", i);
            if has_trade {
                info!("✅ 延迟控制测试成功：订单在{}ms后执行", i);
                break;
            }
        }

        // 如果到了预期的延迟时间还没有执行，继续等待
        if i >= 5 {
            info!("⏰ 已过预期延迟时间(3ms)，继续等待...");
        }
    }

    // 最终检查
    info!("🔍 最终检查...");
    tokio::time::sleep(Duration::from_millis(100)).await;

    // 再次尝试接收
    let mut final_trade_count = 0;
    let mut final_order_update_count = 0;

    while let Ok(trade) = trade_rx.try_recv() {
        final_trade_count += 1;
        info!("✅ 最终收到交易: {} @ {}", trade.id, trade.price);
    }

    while let Ok(order_update) = order_update_rx.try_recv() {
        final_order_update_count += 1;
        info!(
            "📋 最终收到订单更新: {} 状态: {:?}",
            order_update.id, order_update.status
        );
    }

    info!("📊 最终统计:");
    info!("   交易数量: {}", final_trade_count);
    info!("   订单更新数量: {}", final_order_update_count);

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    // 验证结果
    if final_trade_count > 0 {
        info!("✅ 延迟控制功能验证成功");
    } else if final_order_update_count > 0 {
        info!("⚠️ 收到订单更新但没有交易，可能是订单被拒绝或其他状态");
    } else {
        info!("❌ 没有收到任何交易或订单更新");
        panic!("测试失败：没有检测到任何订单活动");
    }

    info!("✅ 调试测试完成");
}

#[tokio::test]
async fn test_order_latency_stats_verification() {
    info!("🔍 开始延迟统计验证测试");

    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 2000,
        max_queue_size: 1000,
        random_latency: false,
    };

    // 创建最小化的测试环境
    let (market_data_tx, market_data_rx) = broadcast::channel(10);
    let (order_tx, order_rx) = mpsc::channel(10);
    let (trade_tx, _trade_rx) = broadcast::channel(10);
    let (order_update_tx, _order_update_rx) = broadcast::channel(10);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(10);

    let account_config = AccountConfig::default();
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        account_config,
    )));

    let playback_config = PlaybackConfig {
        rate_per_second: 0,
        enabled: false,
        batch_size: 1,
        time_alignment: TimeAlignmentConfig::default(),
        order_latency: latency_config,
    };

    let matching_engine = MatchingEngine::new_with_playback_config(
        account_manager,
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        playback_config,
    );

    // 验证延迟统计
    let stats = matching_engine.get_order_latency_stats();
    info!("📊 延迟统计验证:");
    info!("   启用状态: {}", stats.enabled);
    info!("   延迟时间: {}微秒", stats.latency_micros);
    info!("   队列大小: {}", stats.queue_size);
    info!("   随机延迟: {}", stats.random_latency);

    assert!(stats.enabled, "延迟应该被启用");
    assert_eq!(stats.latency_micros, 2000, "延迟时间应该是2ms");
    assert_eq!(stats.queue_size, 0, "初始队列应该为空");
    assert!(!stats.random_latency, "随机延迟应该被禁用");

    // 验证当前市场时间戳
    let current_timestamp = matching_engine.get_current_market_timestamp();
    info!("⏰ 当前市场时间戳: {}", current_timestamp);
    assert_eq!(current_timestamp, 0, "初始市场时间戳应该为0");

    // 清理
    drop(market_data_tx);
    drop(order_tx);

    info!("✅ 延迟统计验证测试完成");
}
