use backtest::types::{BacktestRecorder, BacktestSummary, BacktestTrade, BacktestOrder, BacktestBbo, OrderSide, OrderType, OrderStatus, Price};
use backtest::backtest_summary::HtmlGenerator;
use chrono::Utc;

#[tokio::test]
async fn test_backtest_recorder() {
    let mut recorder = BacktestRecorder::new();

    // 测试开始记录
    let start_time = Utc::now();
    recorder.start_recording(start_time);
    assert!(recorder.is_recording);
    assert_eq!(recorder.start_time, Some(start_time));

    // 测试记录BBO数据
    let bbo = backtest::types::Bbo {
        update_id: 1,
        bid_price: Price::new(50000.0),
        bid_quantity: 1.0,
        ask_price: Price::new(50001.0),
        ask_quantity: 1.0,
        timestamp: Some(Utc::now().timestamp_micros() as u64),
        data_source_type: backtest::config::DataSourceType::BinanceOfficial,
    };

    recorder.record_bbo(&bbo);
    assert_eq!(recorder.bbo_records.len(), 1);

    // 测试记录订单
    let order = backtest::types::Order {
        id: "order1".to_string(),
        client_order_id: "client1".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Buy,
        price: Some(Price::new(50000.0)),
        quantity: 0.1,
        status: OrderStatus::Filled,
        timestamp: Utc::now(),
        execution_info: None,
    };

    recorder.record_order(&order);
    assert_eq!(recorder.orders.len(), 1);

    // 测试记录交易
    let trade_record = backtest::account::types::TradeRecord::new(
        "trade1".to_string(),
        "order1".to_string(),
        "BTCUSDT".to_string(),
        OrderSide::Buy,
        Price::new(50000.0),
        0.1,
        0.1, // 手续费
        "USDT".to_string(),
        true, // is_maker
    );

    recorder.record_trade(&trade_record);
    assert_eq!(recorder.trades.len(), 1);

    // 测试停止记录
    let end_time = Utc::now();
    recorder.stop_recording(end_time);
    assert!(!recorder.is_recording);
    assert_eq!(recorder.end_time, Some(end_time));

    // 测试生成总结
    let summary = recorder.generate_summary();
    assert!(summary.is_some());

    let summary = summary.unwrap();
    assert_eq!(summary.total_orders, 1);
    assert_eq!(summary.total_fills, 1);
    assert_eq!(summary.bbo_records.len(), 1);
    assert_eq!(summary.orders.len(), 1);
    assert_eq!(summary.trades.len(), 1);
}

#[test]
fn test_html_generator() {
    // 创建测试数据
    let start_time = Utc::now();
    let end_time = start_time + chrono::Duration::hours(1);

    let summary = BacktestSummary {
        start_time,
        end_time,
        total_pnl: 100.0,
        total_orders: 5,
        total_fills: 3,
        win_rate: 66.7,
        annual_return: 15.0,
        max_drawdown: 5.0,
        trades: vec![
            BacktestTrade {
                trade_id: "trade1".to_string(),
                order_id: "order1".to_string(),
                symbol: "BTCUSDT".to_string(),
                side: OrderSide::Buy,
                price: Price::new(50000.0),
                quantity: 0.1,
                fee: 0.1,
                fee_asset: "USDT".to_string(),
                timestamp: start_time,
                is_maker: true,
                pnl: 50.0,
            }
        ],
        orders: vec![
            BacktestOrder {
                order_id: "order1".to_string(),
                client_order_id: "client1".to_string(),
                symbol: "BTCUSDT".to_string(),
                order_type: OrderType::Limit,
                side: OrderSide::Buy,
                price: Some(Price::new(50000.0)),
                quantity: 0.1,
                status: OrderStatus::Filled,
                timestamp: start_time,
                filled_price: Some(Price::new(50000.0)),
                filled_quantity: 0.1,
                fee: 0.1,
                fee_asset: "USDT".to_string(),
            }
        ],
        bbo_records: vec![
            BacktestBbo {
                timestamp: start_time,
                bid_price: Price::new(50000.0),
                bid_quantity: 1.0,
                ask_price: Price::new(50001.0),
                ask_quantity: 1.0,
            }
        ],
    };

    // 测试HTML生成
    let html = HtmlGenerator::generate_summary_html(&summary);

    // 验证HTML包含必要的内容
    assert!(html.contains("回测总结报告"));
    assert!(html.contains("统计摘要"));
    assert!(html.contains("K线图与交易点位"));
    assert!(html.contains("交易记录"));
    assert!(html.contains("订单记录"));
    assert!(html.contains("100.00 USDT")); // 总盈亏
    assert!(html.contains("15.00%")); // 年化收益率
    assert!(html.contains("66.7%")); // 胜率
    assert!(html.contains("5.00%")); // 最大回撤
}

#[tokio::test]
async fn test_backtest_summary_integration() {
    // 这个测试模拟完整的回测流程
    use backtest::state::{set_backtest_recorder, get_backtest_recorder};
    use backtest::types::BacktestRecorder;
    use std::sync::Arc;
    use tokio::sync::Mutex;

    // 初始化回测记录器
    let recorder = Arc::new(Mutex::new(BacktestRecorder::new()));
    set_backtest_recorder(recorder.clone()).await;

    // 开始记录
    {
        let mut recorder = recorder.lock().await;
        recorder.start_recording(Utc::now());
    }

    // 模拟记录一些数据
    {
        let mut recorder = recorder.lock().await;

        // 记录BBO
        let bbo = backtest::types::Bbo {
            update_id: 1,
            bid_price: Price::new(50000.0),
            bid_quantity: 1.0,
            ask_price: Price::new(50001.0),
            ask_quantity: 1.0,
            timestamp: Some(Utc::now().timestamp_micros() as u64),
            data_source_type: backtest::config::DataSourceType::BinanceOfficial,
        };
        recorder.record_bbo(&bbo);

        // 记录订单
        let order = backtest::types::Order {
            id: "order1".to_string(),
            client_order_id: "client1".to_string(),
            symbol: "BTCUSDT".to_string(),
            order_type: OrderType::Limit,
            side: OrderSide::Buy,
            price: Some(Price::new(50000.0)),
            quantity: 0.1,
            status: OrderStatus::Filled,
            timestamp: Utc::now(),
            execution_info: None,
        };
        recorder.record_order(&order);

        // 记录交易
        let trade_record = backtest::account::types::TradeRecord::new(
            "trade1".to_string(),
            "order1".to_string(),
            "BTCUSDT".to_string(),
            OrderSide::Buy,
            Price::new(50000.0),
            0.1,
            0.1,
            "USDT".to_string(),
            true,
        );
        recorder.record_trade(&trade_record);
    }

    // 停止记录
    {
        let mut recorder = recorder.lock().await;
        recorder.stop_recording(Utc::now());
    }

    // 验证记录器状态
    {
        let recorder = get_backtest_recorder().await;
        assert!(recorder.is_some());

        let recorder = recorder.unwrap();
        let recorder = recorder.lock().await;
        assert!(!recorder.is_recording);
        assert!(recorder.start_time.is_some());
        assert!(recorder.end_time.is_some());
        assert_eq!(recorder.bbo_records.len(), 1);
        assert_eq!(recorder.orders.len(), 1);
        assert_eq!(recorder.trades.len(), 1);

        // 测试生成总结
        let summary = recorder.generate_summary();
        assert!(summary.is_some());
    }
}
