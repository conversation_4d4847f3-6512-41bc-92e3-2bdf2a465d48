use backtest::account::manager::AccountManager;
use backtest::account::types::AccountConfig;
use backtest::config::{DataSourceType, OrderLatencyConfig, PlaybackConfig, TimeAlignmentConfig};
use backtest::matching::MatchingEngine;
use backtest::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade};
use chrono::Utc;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, Mutex};
use tokio::time::{sleep, Duration, Instant};
use tracing::info;

/// 创建测试用的BBO数据
fn create_test_bbo(timestamp_micros: u64, bid_price: f64, ask_price: f64) -> MarketData {
    MarketData::Bbo(Bbo {
        update_id: 0,
        bid_price: Price::new(bid_price),
        bid_quantity: 10.0,
        ask_price: Price::new(ask_price),
        ask_quantity: 10.0,
        timestamp: Some(timestamp_micros),
        data_source_type: DataSourceType::BinanceTardis,
    })
}

/// 创建测试订单
fn create_test_order(id: &str, side: OrderSide, price: Option<f64>, quantity: f64) -> Order {
    Order {
        id: id.to_string(),
        client_order_id: format!("client_{}", id),
        symbol: "BTCUSDT".to_string(),
        order_type: if price.is_some() {
            OrderType::Limit
        } else {
            OrderType::Market
        },
        side,
        price: price.map(Price::new),
        quantity,
        status: OrderStatus::Pending,
        timestamp: Utc::now(),
        execution_info: None,
    }
}

/// 设置测试环境
async fn setup_test_environment(
    latency_config: OrderLatencyConfig,
) -> (
    MatchingEngine,
    broadcast::Sender<MarketData>,
    mpsc::Sender<Order>,
    broadcast::Receiver<Trade>,
    broadcast::Receiver<Order>,
) {
    // 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(1000);
    let (order_tx, order_rx) = mpsc::channel(1000);
    let (trade_tx, trade_rx) = broadcast::channel(1000);
    let (order_update_tx, order_update_rx) = broadcast::channel(1000);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(1000);

    // 创建账户管理器
    let account_config = AccountConfig::default();
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        account_config,
    )));

    // 创建回放配置
    let playback_config = PlaybackConfig {
        rate_per_second: 0, // 无限制速率
        enabled: false,     // 禁用速率限制
        batch_size: 1,
        time_alignment: TimeAlignmentConfig::default(),
        order_latency: latency_config,
    };

    // 创建撮合引擎
    let matching_engine = MatchingEngine::new_with_playback_config(
        account_manager,
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        playback_config,
    );

    (
        matching_engine,
        market_data_tx,
        order_tx,
        trade_rx,
        order_update_rx,
    )
}

#[tokio::test]
async fn test_order_latency_e2e_basic() {
    tracing_subscriber::fmt::init();
    info!("🚀 开始基本订单延迟端到端测试");

    // 配置5ms延迟
    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 5000, // 5ms
        max_queue_size: 1000,
        random_latency: false,
    };

    let (mut matching_engine, market_data_tx, order_tx, mut trade_rx, _order_update_rx) =
        setup_test_environment(latency_config).await;

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    // 等待引擎启动
    sleep(Duration::from_millis(100)).await;

    // 记录测试开始时间
    let mut execution_times = Vec::new();

    // 发送初始市场数据 (t=0)
    let initial_timestamp = 1000000; // 1秒基准时间戳
    let market_data = create_test_bbo(initial_timestamp, 50000.0, 50100.0);
    market_data_tx.send(market_data).unwrap();

    // 等待市场数据处理
    sleep(Duration::from_millis(10)).await;

    // 提交订单 (应该在t+5ms执行) - 使用限价单确保能成交
    let order_submit_time = Instant::now();
    let order = create_test_order("test_order_1", OrderSide::Buy, Some(50050.0), 1.0); // 限价单，价格略高于ask
    order_tx.send(order).await.unwrap();
    info!("📝 订单已提交，等待延迟执行...");

    // 发送更多市场数据推进时间
    for i in 1..=10 {
        let timestamp = initial_timestamp + (i * 1000); // 每1ms一个数据点
        let market_data = create_test_bbo(timestamp, 50000.0 + i as f64, 50100.0 + i as f64);
        market_data_tx.send(market_data).unwrap();
        sleep(Duration::from_millis(1)).await;
    }

    // 等待交易执行
    let mut trade_received = false;
    let timeout = Duration::from_millis(1000);
    let start_wait = Instant::now();

    while start_wait.elapsed() < timeout {
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    let execution_time = Instant::now();
                    let delay = execution_time.duration_since(order_submit_time);
                    execution_times.push(delay);

                    info!("✅ 交易执行: {} @ {}, 延迟: {:?}",
                          trade.id, trade.price, delay);
                    trade_received = true;
                    break;
                }
            }
            _ = sleep(Duration::from_millis(10)) => {
                // 继续等待
            }
        }
    }

    assert!(trade_received, "订单应该在延迟后执行");

    // 验证延迟时间合理性（考虑到系统处理时间，应该在几十毫秒内）
    let actual_delay = execution_times[0];
    info!("📊 实际延迟: {:?}", actual_delay);

    // 由于是基于市场数据时间戳的延迟，实际系统延迟应该很小
    assert!(
        actual_delay < Duration::from_millis(100),
        "系统处理延迟应该小于100ms，实际: {:?}",
        actual_delay
    );

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 基本延迟测试完成");
}

#[tokio::test]
async fn test_order_latency_e2e_multiple_orders() {
    info!("🚀 开始多订单延迟端到端测试");

    // 配置3ms延迟
    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 3000, // 3ms
        max_queue_size: 1000,
        random_latency: false,
    };

    let (mut matching_engine, market_data_tx, order_tx, mut trade_rx, _order_update_rx) =
        setup_test_environment(latency_config).await;

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    sleep(Duration::from_millis(100)).await;

    let base_timestamp = 2000000; // 2秒基准时间戳
    let mut trade_count = 0;
    let expected_trades = 3;

    // 发送初始市场数据
    let market_data = create_test_bbo(base_timestamp, 50000.0, 50100.0);
    market_data_tx.send(market_data).unwrap();
    sleep(Duration::from_millis(10)).await;

    // 提交3个订单，间隔1ms
    for i in 0..3 {
        let order = create_test_order(
            &format!("order_{}", i + 1),
            OrderSide::Buy,
            Some(50050.0), // 限价单，价格略高于ask
            0.1,
        );
        order_tx.send(order).await.unwrap();
        info!("📝 提交订单 {}", i + 1);

        // 发送对应的市场数据推进时间
        let timestamp = base_timestamp + ((i + 1) * 1000); // 每1ms
        let market_data = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(market_data).unwrap();

        sleep(Duration::from_millis(2)).await;
    }

    // 继续发送市场数据确保所有订单都能执行
    for i in 4..=10 {
        let timestamp = base_timestamp + (i * 1000);
        let market_data = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(market_data).unwrap();
        sleep(Duration::from_millis(1)).await;
    }

    // 收集交易结果
    let timeout = Duration::from_millis(2000);
    let start_wait = Instant::now();

    while start_wait.elapsed() < timeout && trade_count < expected_trades {
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    trade_count += 1;
                    info!("✅ 收到交易 {}: {} @ {}",
                          trade_count, trade.id, trade.price);
                }
            }
            _ = sleep(Duration::from_millis(10)) => {
                // 继续等待
            }
        }
    }

    assert_eq!(
        trade_count, expected_trades,
        "应该收到{}笔交易，实际收到{}",
        expected_trades, trade_count
    );

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 多订单延迟测试完成");
}

#[tokio::test]
async fn test_order_latency_disabled() {
    info!("🚀 开始延迟禁用测试");

    // 禁用延迟
    let latency_config = OrderLatencyConfig {
        enabled: false,
        latency_micros: 5000,
        max_queue_size: 1000,
        random_latency: false,
    };

    let (mut matching_engine, market_data_tx, order_tx, mut trade_rx, _order_update_rx) =
        setup_test_environment(latency_config).await;

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    sleep(Duration::from_millis(100)).await;

    // 发送市场数据
    let timestamp = 3000000;
    let market_data = create_test_bbo(timestamp, 50000.0, 50100.0);
    market_data_tx.send(market_data).unwrap();
    sleep(Duration::from_millis(10)).await;

    // 提交订单
    let order_submit_time = Instant::now();
    let order = create_test_order("immediate_order", OrderSide::Buy, Some(50050.0), 1.0);
    order_tx.send(order).await.unwrap();
    info!("📝 订单已提交（延迟禁用）");

    // 等待立即执行
    let mut trade_received = false;
    let timeout = Duration::from_millis(500);
    let start_wait = Instant::now();

    while start_wait.elapsed() < timeout {
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    let execution_time = Instant::now();
                    let delay = execution_time.duration_since(order_submit_time);

                    info!("✅ 立即执行: {} @ {}, 延迟: {:?}",
                          trade.id, trade.price, delay);

                    // 禁用延迟时应该很快执行
                    assert!(delay < Duration::from_millis(50),
                            "禁用延迟时执行应该很快，实际: {:?}", delay);

                    trade_received = true;
                    break;
                }
            }
            _ = sleep(Duration::from_millis(10)) => {
                // 继续等待
            }
        }
    }

    assert!(trade_received, "禁用延迟时订单应该立即执行");

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 延迟禁用测试完成");
}

#[tokio::test]
async fn test_order_latency_queue_verification() {
    info!("🚀 开始延迟队列验证测试");

    // 配置延迟但不启动完整的撮合引擎
    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 3000, // 3ms
        max_queue_size: 1000,
        random_latency: false,
    };

    let (matching_engine, market_data_tx, order_tx, _trade_rx, _order_update_rx) =
        setup_test_environment(latency_config).await;

    // 不启动引擎，直接测试延迟队列功能

    // 发送市场数据更新时间戳
    let base_timestamp = 1000000;
    let market_data = create_test_bbo(base_timestamp, 50000.0, 50100.0);
    market_data_tx.send(market_data).unwrap();

    // 手动处理一次市场数据来更新时间戳
    // 这里我们需要直接访问matching engine的方法

    // 检查延迟统计
    let stats = matching_engine.get_order_latency_stats();
    info!(
        "📊 初始延迟统计: 启用={}, 队列大小={}",
        stats.enabled, stats.queue_size
    );

    assert!(stats.enabled, "延迟模拟应该被启用");
    assert_eq!(stats.queue_size, 0, "初始队列应该为空");
    assert_eq!(stats.latency_micros, 3000, "延迟时间应该是3ms");

    // 提交订单到队列
    let order = create_test_order("queue_test_order", OrderSide::Buy, Some(50050.0), 1.0);
    order_tx.send(order).await.unwrap();

    // 等待一下让订单被处理
    sleep(Duration::from_millis(50)).await;

    // 再次检查统计（这个测试可能不会显示队列中的订单，因为我们没有启动完整的引擎）
    let stats_after = matching_engine.get_order_latency_stats();
    info!("📊 提交订单后统计: 队列大小={}", stats_after.queue_size);

    info!("✅ 延迟队列验证测试完成");
}
