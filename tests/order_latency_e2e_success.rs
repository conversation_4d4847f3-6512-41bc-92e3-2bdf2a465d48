use backtest::account::manager::AccountManager;
use backtest::account::types::AccountConfig;
use backtest::config::{DataSourceType, OrderLatencyConfig, PlaybackConfig, TimeAlignmentConfig};
use backtest::matching::MatchingEngine;
use backtest::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price};
use chrono::Utc;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{broadcast, mpsc, Mutex};
use tracing::info;

/// 创建测试用的BBO数据
fn create_test_bbo(timestamp_micros: u64, bid_price: f64, ask_price: f64) -> MarketData {
    MarketData::Bbo(Bbo {
        update_id: 0,
        bid_price: Price::new(bid_price),
        bid_quantity: 100.0,
        ask_price: Price::new(ask_price),
        ask_quantity: 100.0,
        timestamp: Some(timestamp_micros),
        data_source_type: DataSourceType::BinanceTardis,
    })
}

/// 创建测试订单
fn create_test_order(id: &str, side: OrderSide, price: Option<f64>, quantity: f64) -> Order {
    Order {
        id: id.to_string(),
        client_order_id: format!("client_{}", id),
        symbol: "BTCUSDT".to_string(),
        order_type: if price.is_some() {
            OrderType::Limit
        } else {
            OrderType::Market
        },
        side,
        price: price.map(Price::new),
        quantity,
        status: OrderStatus::Pending,
        timestamp: Utc::now(),
        execution_info: None,
    }
}

#[tokio::test]
async fn test_order_latency_e2e_success() {
    tracing_subscriber::fmt::init();
    info!("🚀 开始成功的订单延迟端到端测试");

    // 配置3ms延迟
    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 3000, // 3ms
        max_queue_size: 1000,
        random_latency: false,
    };

    // 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(1000);
    let (order_tx, order_rx) = mpsc::channel(1000);
    let (trade_tx, _trade_rx) = broadcast::channel(1000);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(1000);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(1000);

    // 创建账户管理器
    let mut account_config = AccountConfig::default();
    account_config.initial_balance = 1000000.0; // 100万USDT
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        account_config,
    )));

    // 创建回放配置
    let playback_config = PlaybackConfig {
        rate_per_second: 0,
        enabled: false,
        batch_size: 1,
        time_alignment: TimeAlignmentConfig::default(),
        order_latency: latency_config,
    };

    // 创建撮合引擎
    let mut matching_engine = MatchingEngine::new_with_playback_config(
        account_manager,
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        playback_config,
    );

    // 验证延迟配置
    let stats = matching_engine.get_order_latency_stats();
    info!("📊 延迟配置验证:");
    info!("   启用: {}", stats.enabled);
    info!("   延迟时间: {}微秒", stats.latency_micros);
    assert!(stats.enabled, "延迟模拟应该被启用");
    assert_eq!(stats.latency_micros, 3000, "延迟时间应该是3ms");

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    // 等待引擎启动
    tokio::time::sleep(Duration::from_millis(100)).await;

    // 测试场景：验证延迟控制
    let base_timestamp = 1000000; // 1秒基准时间戳
    let mut order_updates_received = 0;
    let mut delay_verified = false;

    // 步骤1: 发送初始市场数据
    info!("📈 步骤1: 发送初始市场数据");
    let initial_bbo = create_test_bbo(base_timestamp, 50000.0, 50100.0);
    market_data_tx.send(initial_bbo).unwrap();
    tokio::time::sleep(Duration::from_millis(20)).await;

    // 步骤2: 提交订单
    info!("📝 步骤2: 提交订单");
    let order_submit_market_time = base_timestamp;
    let order = create_test_order("latency_test_order", OrderSide::Buy, Some(50050.0), 1.0);
    order_tx.send(order).await.unwrap();
    info!("📝 订单已提交，市场时间戳: {}", order_submit_market_time);

    // 步骤3: 推进市场数据时间，验证延迟控制
    info!("⏰ 步骤3: 推进市场数据时间验证延迟");

    // 在延迟时间之前（1ms和2ms），不应该有订单处理
    for i in 1..=2 {
        let timestamp = base_timestamp + (i * 1000);
        let bbo = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(bbo).unwrap();
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 检查是否有订单更新（不应该有）
        if let Ok(order_update) = order_update_rx.try_recv() {
            panic!(
                "在延迟时间之前不应该有订单处理，但在{}ms时收到了订单更新: {}",
                i, order_update.id
            );
        }
        info!("✓ {}ms时没有订单处理（符合预期）", i);
    }

    // 在延迟时间之后（3ms及以后），应该有订单处理
    for i in 3..=5 {
        let timestamp = base_timestamp + (i * 1000);
        let bbo = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(bbo).unwrap();
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 检查是否有订单更新
        if let Ok(order_update) = order_update_rx.try_recv() {
            order_updates_received += 1;
            let market_delay = timestamp - order_submit_market_time;

            info!("✅ 在{}ms时收到订单处理!", i);
            info!("   订单ID: {}", order_update.id);
            info!("   订单状态: {:?}", order_update.status);
            info!(
                "   市场数据延迟: {}微秒 ({}ms)",
                market_delay,
                market_delay / 1000
            );

            // 验证延迟是否符合预期（应该在3ms或之后）
            if market_delay >= 3000 {
                delay_verified = true;
                info!(
                    "✅ 延迟控制验证成功：订单在{}ms后被处理",
                    market_delay / 1000
                );
                break;
            }
        }
    }

    // 步骤4: 最终验证
    info!("📊 步骤4: 最终验证");

    // 等待一下确保所有处理完成
    tokio::time::sleep(Duration::from_millis(50)).await;

    // 收集剩余的订单更新
    while let Ok(order_update) = order_update_rx.try_recv() {
        order_updates_received += 1;
        info!(
            "📋 额外收到订单更新: {} 状态: {:?}",
            order_update.id, order_update.status
        );
    }

    // 验证结果
    info!("📈 测试结果总结:");
    info!("   订单更新数量: {}", order_updates_received);
    info!("   延迟验证: {}", delay_verified);

    assert!(order_updates_received > 0, "应该收到至少一个订单更新");
    assert!(delay_verified, "应该验证延迟控制有效");

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 成功的订单延迟端到端测试完成");
}

#[tokio::test]
async fn test_order_latency_timing_precision() {
    info!("🚀 开始延迟时间精度测试");

    // 配置2ms延迟
    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 2000, // 2ms
        max_queue_size: 1000,
        random_latency: false,
    };

    // 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(1000);
    let (order_tx, order_rx) = mpsc::channel(1000);
    let (trade_tx, _trade_rx) = broadcast::channel(1000);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(1000);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(1000);

    let account_config = AccountConfig::default();
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        account_config,
    )));

    let playback_config = PlaybackConfig {
        rate_per_second: 0,
        enabled: false,
        batch_size: 1,
        time_alignment: TimeAlignmentConfig::default(),
        order_latency: latency_config,
    };

    let mut matching_engine = MatchingEngine::new_with_playback_config(
        account_manager,
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        playback_config,
    );

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });
    tokio::time::sleep(Duration::from_millis(100)).await;

    let base_timestamp = 2000000; // 2秒基准时间戳
    let mut execution_times = Vec::new();

    // 发送初始市场数据
    let initial_bbo = create_test_bbo(base_timestamp, 50000.0, 50100.0);
    market_data_tx.send(initial_bbo).unwrap();
    tokio::time::sleep(Duration::from_millis(10)).await;

    // 提交3个订单，每个间隔1ms
    for i in 0..3 {
        let order = create_test_order(
            &format!("timing_order_{}", i + 1),
            OrderSide::Buy,
            Some(50050.0),
            0.1,
        );
        let submit_time = base_timestamp + (i * 1000); // 每个订单间隔1ms
        order_tx.send(order).await.unwrap();
        info!("📝 提交订单{} 在市场时间戳: {}", i + 1, submit_time);

        // 发送对应的市场数据
        let bbo = create_test_bbo(submit_time, 50000.0, 50100.0);
        market_data_tx.send(bbo).unwrap();
        tokio::time::sleep(Duration::from_millis(5)).await;
    }

    // 推进时间以触发延迟订单执行
    for i in 3..=8 {
        let timestamp = base_timestamp + (i * 1000);
        let bbo = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(bbo).unwrap();
        tokio::time::sleep(Duration::from_millis(5)).await;

        // 检查订单更新
        while let Ok(order_update) = order_update_rx.try_recv() {
            execution_times.push(timestamp);
            info!("⏰ 订单执行: {} 在时间戳: {}", order_update.id, timestamp);
        }
    }

    // 验证执行时间
    info!("📊 执行时间分析:");
    for (i, &exec_time) in execution_times.iter().enumerate() {
        let submit_time = base_timestamp + (i as u64 * 1000);
        let delay = exec_time - submit_time;
        info!(
            "   订单{}: 提交={}, 执行={}, 延迟={}微秒",
            i + 1,
            submit_time,
            exec_time,
            delay
        );

        // 验证延迟是否符合预期（2ms = 2000微秒）
        assert!(delay >= 2000, "延迟应该至少2ms，实际: {}微秒", delay);
        assert!(delay <= 3000, "延迟应该不超过3ms，实际: {}微秒", delay);
    }

    assert!(execution_times.len() >= 1, "应该至少有一个订单被执行");

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 延迟时间精度测试完成");
}
