use backtest::account::manager::AccountManager;
use backtest::account::types::AccountConfig;
use backtest::config::{DataSourceType, OrderLatencyConfig, PlaybackConfig, TimeAlignmentConfig};
use backtest::matching::MatchingEngine;
use backtest::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade};
use chrono::Utc;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{broadcast, mpsc, Mutex};
use tracing::info;

/// 创建测试用的BBO数据
fn create_test_bbo(timestamp_micros: u64, bid_price: f64, ask_price: f64) -> MarketData {
    MarketData::Bbo(Bbo {
        update_id: 0,
        bid_price: Price::new(bid_price),
        bid_quantity: 100.0, // 增加流动性
        ask_price: Price::new(ask_price),
        ask_quantity: 100.0, // 增加流动性
        timestamp: Some(timestamp_micros),
        data_source_type: DataSourceType::BinanceTardis,
    })
}

/// 创建测试订单
fn create_test_order(id: &str, side: OrderSide, price: Option<f64>, quantity: f64) -> Order {
    Order {
        id: id.to_string(),
        client_order_id: format!("client_{}", id),
        symbol: "BTCUSDT".to_string(),
        order_type: if price.is_some() {
            OrderType::Limit
        } else {
            OrderType::Market
        },
        side,
        price: price.map(Price::new),
        quantity,
        status: OrderStatus::Pending,
        timestamp: Utc::now(),
        execution_info: None,
    }
}

/// 设置测试环境
async fn setup_test_environment(
    latency_config: OrderLatencyConfig,
) -> (
    MatchingEngine,
    broadcast::Sender<MarketData>,
    mpsc::Sender<Order>,
    broadcast::Receiver<Trade>,
    broadcast::Receiver<Order>,
) {
    // 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(1000);
    let (order_tx, order_rx) = mpsc::channel(1000);
    let (trade_tx, trade_rx) = broadcast::channel(1000);
    let (order_update_tx, order_update_rx) = broadcast::channel(1000);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(1000);

    // 创建账户管理器
    let account_config = AccountConfig::default();
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        account_config,
    )));

    // 创建回放配置
    let playback_config = PlaybackConfig {
        rate_per_second: 0, // 无限制速率
        enabled: false,     // 禁用速率限制
        batch_size: 1,
        time_alignment: TimeAlignmentConfig::default(),
        order_latency: latency_config,
    };

    // 创建撮合引擎
    let matching_engine = MatchingEngine::new_with_playback_config(
        account_manager,
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        playback_config,
    );

    (
        matching_engine,
        market_data_tx,
        order_tx,
        trade_rx,
        order_update_rx,
    )
}

#[tokio::test]
async fn test_order_latency_e2e_comprehensive() {
    tracing_subscriber::fmt::init();
    info!("🚀 开始综合订单延迟端到端测试");

    // 配置5ms延迟
    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 5000, // 5ms
        max_queue_size: 1000,
        random_latency: false,
    };

    let (mut matching_engine, market_data_tx, order_tx, mut trade_rx, mut order_update_rx) =
        setup_test_environment(latency_config).await;

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    // 等待引擎启动
    tokio::time::sleep(Duration::from_millis(100)).await;

    // 测试场景：验证延迟控制的有效性
    let base_timestamp = 1000000; // 1秒基准时间戳
    let mut market_timestamp = base_timestamp;

    // 步骤1: 发送初始市场数据建立价格
    info!("📊 步骤1: 发送初始市场数据");
    let initial_bbo = create_test_bbo(market_timestamp, 50000.0, 50100.0);
    market_data_tx.send(initial_bbo).unwrap();
    tokio::time::sleep(Duration::from_millis(10)).await;

    // 步骤2: 提交订单并记录时间
    info!("📝 步骤2: 提交订单");
    let order_submit_time = Instant::now();
    let order_submit_market_time = market_timestamp;

    // 提交一个买单，价格略高于ask确保能成交
    let order = create_test_order("latency_test_order", OrderSide::Buy, Some(50050.0), 1.0);
    order_tx.send(order).await.unwrap();
    info!(
        "📝 订单已提交，市场时间戳: {}, 预期执行时间戳: {}",
        order_submit_market_time,
        order_submit_market_time + 5000
    );

    // 步骤3: 逐步推进市场数据时间，模拟真实市场数据流
    info!("⏰ 步骤3: 推进市场数据时间");
    let mut execution_detected = false;
    let mut trade_execution_time = None;
    let mut order_updates_received = 0;

    // 推进时间，每1ms发送一次市场数据，持续10ms
    for i in 1..=10 {
        market_timestamp = base_timestamp + (i * 1000); // 每次增加1ms
        let bbo = create_test_bbo(market_timestamp, 50000.0 + i as f64, 50100.0 + i as f64);
        market_data_tx.send(bbo).unwrap();

        info!("📈 发送市场数据，时间戳: {} (+{}ms)", market_timestamp, i);

        // 等待一小段时间让系统处理
        tokio::time::sleep(Duration::from_millis(5)).await;

        // 检查是否有交易或订单更新
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    if !execution_detected {
                        execution_detected = true;
                        trade_execution_time = Some(Instant::now());
                        let market_delay = market_timestamp - order_submit_market_time;
                        let system_delay = trade_execution_time.unwrap().duration_since(order_submit_time);

                        info!("✅ 交易执行检测到!");
                        info!("   交易ID: {}", trade.id);
                        info!("   交易价格: {}", trade.price);
                        info!("   市场数据延迟: {}微秒 ({}ms)", market_delay, market_delay / 1000);
                        info!("   系统处理延迟: {:?}", system_delay);

                        // 验证市场数据延迟是否符合预期（应该在5ms左右）
                        assert!(market_delay >= 5000, "市场数据延迟应该至少5ms，实际: {}微秒", market_delay);
                        assert!(market_delay <= 6000, "市场数据延迟应该不超过6ms，实际: {}微秒", market_delay);
                    }
                }
            }
            order_result = order_update_rx.recv() => {
                if let Ok(order_update) = order_result {
                    order_updates_received += 1;
                    info!("📋 订单更新: {} 状态: {:?}", order_update.id, order_update.status);
                }
            }
            _ = tokio::time::sleep(Duration::from_millis(1)) => {
                // 继续下一轮
            }
        }

        // 如果已经检测到执行，可以提前结束
        if execution_detected && i >= 6 {
            break;
        }
    }

    // 步骤4: 验证结果
    info!("📊 步骤4: 验证测试结果");
    assert!(execution_detected, "应该检测到订单执行");
    assert!(order_updates_received > 0, "应该收到订单状态更新");

    if let Some(exec_time) = trade_execution_time {
        let total_system_delay = exec_time.duration_since(order_submit_time);
        info!("📈 总系统处理时间: {:?}", total_system_delay);

        // 系统处理时间应该合理（通常在几十毫秒内）
        assert!(
            total_system_delay < Duration::from_millis(200),
            "系统处理时间应该合理，实际: {:?}",
            total_system_delay
        );
    }

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 综合订单延迟端到端测试完成");
}

#[tokio::test]
async fn test_order_latency_disabled_e2e() {
    info!("🚀 开始延迟禁用端到端测试");

    // 禁用延迟
    let latency_config = OrderLatencyConfig {
        enabled: false,
        latency_micros: 5000,
        max_queue_size: 1000,
        random_latency: false,
    };

    let (mut matching_engine, market_data_tx, order_tx, mut trade_rx, _order_update_rx) =
        setup_test_environment(latency_config).await;

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    tokio::time::sleep(Duration::from_millis(100)).await;

    // 发送市场数据
    let timestamp = 2000000;
    let bbo = create_test_bbo(timestamp, 50000.0, 50100.0);
    market_data_tx.send(bbo).unwrap();
    tokio::time::sleep(Duration::from_millis(10)).await;

    // 提交订单
    let order_submit_time = Instant::now();
    let order = create_test_order("immediate_order", OrderSide::Buy, Some(50050.0), 1.0);
    order_tx.send(order).await.unwrap();
    info!("📝 订单已提交（延迟禁用）");

    // 等待立即执行
    let mut trade_received = false;
    let timeout = Duration::from_millis(500);
    let start_wait = Instant::now();

    while start_wait.elapsed() < timeout {
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    let execution_time = Instant::now();
                    let delay = execution_time.duration_since(order_submit_time);

                    info!("✅ 立即执行: {} @ {}, 延迟: {:?}",
                          trade.id, trade.price, delay);

                    // 禁用延迟时应该很快执行
                    assert!(delay < Duration::from_millis(100),
                            "禁用延迟时执行应该很快，实际: {:?}", delay);

                    trade_received = true;
                    break;
                }
            }
            _ = tokio::time::sleep(Duration::from_millis(10)) => {
                // 继续等待
            }
        }
    }

    assert!(trade_received, "禁用延迟时订单应该立即执行");

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 延迟禁用端到端测试完成");
}

#[tokio::test]
async fn test_order_latency_multiple_orders_e2e() {
    info!("🚀 开始多订单延迟端到端测试");

    // 配置3ms延迟
    let latency_config = OrderLatencyConfig {
        enabled: true,
        latency_micros: 3000, // 3ms
        max_queue_size: 1000,
        random_latency: false,
    };

    let (mut matching_engine, market_data_tx, order_tx, mut trade_rx, _order_update_rx) =
        setup_test_environment(latency_config).await;

    // 启动撮合引擎
    let engine_handle = tokio::spawn(async move { matching_engine.start().await });

    tokio::time::sleep(Duration::from_millis(100)).await;

    let base_timestamp = 3000000; // 3秒基准时间戳
    let mut trade_count = 0;
    let expected_trades = 3;
    let mut execution_times = Vec::new();

    // 发送初始市场数据
    let market_data = create_test_bbo(base_timestamp, 50000.0, 50100.0);
    market_data_tx.send(market_data).unwrap();
    tokio::time::sleep(Duration::from_millis(10)).await;

    // 提交3个订单，每个间隔1ms的市场时间
    for i in 0..3 {
        let order = create_test_order(
            &format!("multi_order_{}", i + 1),
            OrderSide::Buy,
            Some(50050.0), // 限价单，价格略高于ask
            0.1,
        );
        order_tx.send(order).await.unwrap();
        info!("📝 提交订单 {}", i + 1);

        // 发送对应的市场数据推进时间
        let timestamp = base_timestamp + ((i + 1) * 1000); // 每1ms
        let market_data = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(market_data).unwrap();

        tokio::time::sleep(Duration::from_millis(5)).await;
    }

    // 继续发送市场数据确保所有订单都能执行
    for i in 4..=10 {
        let timestamp = base_timestamp + (i * 1000);
        let market_data = create_test_bbo(timestamp, 50000.0, 50100.0);
        market_data_tx.send(market_data).unwrap();
        tokio::time::sleep(Duration::from_millis(2)).await;
    }

    // 收集交易结果
    let timeout = Duration::from_millis(2000);
    let start_wait = Instant::now();

    while start_wait.elapsed() < timeout && trade_count < expected_trades {
        tokio::select! {
            trade_result = trade_rx.recv() => {
                if let Ok(trade) = trade_result {
                    trade_count += 1;
                    execution_times.push(Instant::now());
                    info!("✅ 收到交易 {}: {} @ {}",
                          trade_count, trade.id, trade.price);
                }
            }
            _ = tokio::time::sleep(Duration::from_millis(10)) => {
                // 继续等待
            }
        }
    }

    assert_eq!(
        trade_count, expected_trades,
        "应该收到{}笔交易，实际收到{}",
        expected_trades, trade_count
    );

    info!("📊 执行时间间隔分析:");
    for i in 1..execution_times.len() {
        let interval = execution_times[i].duration_since(execution_times[i - 1]);
        info!("   交易{} -> 交易{}: {:?}", i, i + 1, interval);
    }

    // 清理
    drop(market_data_tx);
    drop(order_tx);
    let _ = engine_handle.await;

    info!("✅ 多订单延迟端到端测试完成");
}
