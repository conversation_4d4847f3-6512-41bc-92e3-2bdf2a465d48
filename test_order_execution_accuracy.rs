use backtest::account::manager::AccountManager;
use backtest::account::types::AccountConfig;
use backtest::config::{DataSourceType, PlaybackConfig};
use backtest::matching::{MatchingEngine, OrderBook};
use backtest::state::{get_backtest_recorder, set_backtest_recorder};
use backtest::types::{Bbo, MarketData, Order, OrderSide, OrderStatus, OrderType, Price, Trade};
use chrono::{DateTime, Utc};
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc, Mutex};

/// 测试订单成交过程的准确性
#[tokio::test]
async fn test_order_execution_accuracy() {
    println!("🧪 测试订单成交过程准确性");
    println!("==========================");

    // 1. 初始化回测记录器
    let recorder = Arc::new(Mutex::new(backtest::types::BacktestRecorder::new()));
    set_backtest_recorder(recorder.clone()).await;
    {
        let mut recorder = recorder.lock().await;
        recorder.start_recording(Utc::now());
    }

    // 2. 创建测试环境
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        AccountConfig::default(),
    )));
    let orderbook = Arc::new(Mutex::new(OrderBook::new()));

    // 3. 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(100);
    let (order_tx, order_rx) = mpsc::channel(100);
    let (trade_tx, mut trade_rx) = broadcast::channel(100);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);
    let (cancel_order_tx, cancel_order_rx) = mpsc::channel(100);

    // 4. 创建撮合引擎
    let mut engine = MatchingEngine::new_with_playback_config(
        orderbook,
        account_manager,
        market_data_rx,
        order_rx,
        cancel_order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        PlaybackConfig::default(),
    );

    // 5. 发送市场数据（BBO）
    let market_timestamp = ****************; // 2022-01-01 00:00:00 UTC (微秒)
    let bbo = Bbo {
        update_id: 1,
        bid_price: Price::new(50000.0),
        bid_quantity: 1.0,
        ask_price: Price::new(50001.0),
        ask_quantity: 1.0,
        timestamp: Some(market_timestamp),
        data_source_type: DataSourceType::BinanceTardis,
    };

    let market_data = MarketData::Bbo(bbo);
    market_data_tx.send(market_data).unwrap();

    // 6. 等待市场数据处理
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 7. 创建测试订单
    let order = Order {
        id: "test_order_1".to_string(),
        client_order_id: "client_test_1".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Buy,
        price: Some(Price::new(50000.5)), // 介于bid和ask之间
        quantity: 0.1,
        status: OrderStatus::Pending,
        timestamp: DateTime::from_timestamp_micros(market_timestamp as i64).unwrap(),
        execution_info: None,
    };

    println!("📊 市场数据:");
    println!("   Bid: {}", bbo.bid_price.value());
    println!("   Ask: {}", bbo.ask_price.value());
    println!("   时间戳: {}", market_timestamp);

    println!("📝 订单信息:");
    println!("   订单ID: {}", order.id);
    println!("   价格: {}", order.price.unwrap().value());
    println!("   数量: {}", order.quantity);
    println!("   时间戳: {}", order.timestamp);

    // 8. 发送订单
    order_tx.send(order).await.unwrap();

    // 9. 等待订单处理
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 10. 检查订单更新
    if let Ok(order_update) = order_update_rx.try_recv() {
        println!("📋 订单更新:");
        println!("   订单ID: {}", order_update.id);
        println!("   状态: {:?}", order_update.status);
        println!("   时间戳: {}", order_update.timestamp);
        if let Some(exec_info) = &order_update.execution_info {
            println!("   成交数量: {}", exec_info.filled_quantity);
            if let Some(avg_price) = exec_info.average_price {
                println!("   平均价格: {}", avg_price.value());
            }
        }
    }

    // 11. 检查成交
    if let Ok(trade) = trade_rx.try_recv() {
        println!("💰 成交信息:");
        println!("   成交ID: {}", trade.id);
        println!("   价格: {}", trade.price.value());
        println!("   数量: {}", trade.quantity);
        println!("   时间戳: {:?}", trade.timestamp);
    }

    // 12. 检查回测记录
    {
        let recorder = recorder.lock().await;
        println!("📊 回测记录:");
        println!("   订单数量: {}", recorder.orders.len());
        println!("   成交数量: {}", recorder.trades.len());

        if let Some(order_record) = recorder.orders.first() {
            println!("   第一个订单:");
            println!("     订单ID: {}", order_record.order_id);
            println!("     时间戳: {}", order_record.timestamp);
            println!("     价格: {:?}", order_record.price);
            println!("     成交价格: {:?}", order_record.filled_price);
            println!("     成交数量: {}", order_record.filled_quantity);
        }

        if let Some(trade_record) = recorder.trades.first() {
            println!("   第一个成交:");
            println!("     成交ID: {}", trade_record.trade_id);
            println!("     时间戳: {}", trade_record.timestamp);
            println!("     价格: {}", trade_record.price.value());
            println!("     数量: {}", trade_record.quantity);
        }
    }

    println!("✅ 测试完成");
}

/// 测试时间戳一致性
#[tokio::test]
async fn test_timestamp_consistency() {
    println!("🧪 测试时间戳一致性");
    println!("==================");

    // 1. 初始化回测记录器
    let recorder = Arc::new(Mutex::new(backtest::types::BacktestRecorder::new()));
    set_backtest_recorder(recorder.clone()).await;
    {
        let mut recorder = recorder.lock().await;
        recorder.start_recording(Utc::now());
    }

    // 2. 创建测试环境
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        AccountConfig::default(),
    )));
    let orderbook = Arc::new(Mutex::new(OrderBook::new()));

    // 3. 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(100);
    let (order_tx, order_rx) = mpsc::channel(100);
    let (trade_tx, mut trade_rx) = broadcast::channel(100);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);
    let (cancel_order_tx, cancel_order_rx) = mpsc::channel(100);

    // 4. 创建撮合引擎
    let mut engine = MatchingEngine::new_with_playback_config(
        orderbook,
        account_manager,
        market_data_rx,
        order_rx,
        cancel_order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        PlaybackConfig::default(),
    );

    // 5. 发送多个时间点的市场数据
    let timestamps = vec![
        ****************, // 2022-01-01 00:00:00
        ****************, // 2022-01-01 00:00:01
        ****************, // 2022-01-01 00:00:02
    ];

    for (i, &timestamp) in timestamps.iter().enumerate() {
        let bbo = Bbo {
            update_id: i as u64 + 1,
            bid_price: Price::new(50000.0 + i as f64),
            bid_quantity: 1.0,
            ask_price: Price::new(50001.0 + i as f64),
            ask_quantity: 1.0,
            timestamp: Some(timestamp),
            data_source_type: DataSourceType::BinanceTardis,
        };

        let market_data = MarketData::Bbo(bbo);
        market_data_tx.send(market_data).unwrap();
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    }

    // 6. 创建订单（使用最后一个时间戳）
    let last_timestamp = timestamps.last().unwrap();
    let order = Order {
        id: "test_order_timestamp".to_string(),
        client_order_id: "client_test_timestamp".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Buy,
        price: Some(Price::new(50002.0)), // 高于最后一个ask价格
        quantity: 0.1,
        status: OrderStatus::Pending,
        timestamp: DateTime::from_timestamp_micros(*last_timestamp as i64).unwrap(),
        execution_info: None,
    };

    println!("📊 市场数据时间戳:");
    for (i, &timestamp) in timestamps.iter().enumerate() {
        let dt = DateTime::from_timestamp_micros(timestamp as i64).unwrap();
        println!("   {}: {} ({})", i + 1, dt, timestamp);
    }

    println!("📝 订单时间戳: {}", order.timestamp);

    // 7. 发送订单
    order_tx.send(order).await.unwrap();
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 8. 检查时间戳一致性
    {
        let recorder = recorder.lock().await;
        if let Some(order_record) = recorder.orders.first() {
            println!("📋 记录的订单时间戳: {}", order_record.timestamp);
            assert_eq!(
                order_record.timestamp,
                DateTime::from_timestamp_micros(*last_timestamp as i64).unwrap(),
                "订单时间戳应该与最后一个市场数据时间戳一致"
            );
        }

        if let Some(trade_record) = recorder.trades.first() {
            println!("💰 记录的成交时间戳: {}", trade_record.timestamp);
            assert_eq!(
                trade_record.timestamp,
                DateTime::from_timestamp_micros(*last_timestamp as i64).unwrap(),
                "成交时间戳应该与最后一个市场数据时间戳一致"
            );
        }
    }

    println!("✅ 时间戳一致性测试完成");
}

/// 测试价格准确性
#[tokio::test]
async fn test_price_accuracy() {
    println!("🧪 测试价格准确性");
    println!("==================");

    // 1. 初始化回测记录器
    let recorder = Arc::new(Mutex::new(backtest::types::BacktestRecorder::new()));
    set_backtest_recorder(recorder.clone()).await;
    {
        let mut recorder = recorder.lock().await;
        recorder.start_recording(Utc::now());
    }

    // 2. 创建测试环境
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        AccountConfig::default(),
    )));
    let orderbook = Arc::new(Mutex::new(OrderBook::new()));

    // 3. 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(100);
    let (order_tx, order_rx) = mpsc::channel(100);
    let (trade_tx, mut trade_rx) = broadcast::channel(100);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);
    let (cancel_order_tx, cancel_order_rx) = mpsc::channel(100);

    // 4. 创建撮合引擎
    let mut engine = MatchingEngine::new_with_playback_config(
        orderbook,
        account_manager,
        market_data_rx,
        order_rx,
        cancel_order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        PlaybackConfig::default(),
    );

    // 5. 发送市场数据
    let market_timestamp = ****************;
    let bbo = Bbo {
        update_id: 1,
        bid_price: Price::new(50000.0),
        bid_quantity: 1.0,
        ask_price: Price::new(50001.0),
        ask_quantity: 1.0,
        timestamp: Some(market_timestamp),
        data_source_type: DataSourceType::BinanceTardis,
    };

    let market_data = MarketData::Bbo(bbo);
    market_data_tx.send(market_data).unwrap();
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 6. 创建买单（价格高于ask，应该以ask价格成交）
    let buy_order = Order {
        id: "test_buy_order".to_string(),
        client_order_id: "client_test_buy".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Buy,
        price: Some(Price::new(50002.0)), // 高于ask价格
        quantity: 0.1,
        status: OrderStatus::Pending,
        timestamp: DateTime::from_timestamp_micros(market_timestamp as i64).unwrap(),
        execution_info: None,
    };

    // 7. 创建卖单（价格低于bid，应该以bid价格成交）
    let sell_order = Order {
        id: "test_sell_order".to_string(),
        client_order_id: "client_test_sell".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Sell,
        price: Some(Price::new(49999.0)), // 低于bid价格
        quantity: 0.1,
        status: OrderStatus::Pending,
        timestamp: DateTime::from_timestamp_micros(market_timestamp as i64).unwrap(),
        execution_info: None,
    };

    println!("📊 市场数据:");
    println!("   Bid: {}", bbo.bid_price.value());
    println!("   Ask: {}", bbo.ask_price.value());

    println!("📝 买单价格: {}", buy_order.price.unwrap().value());
    println!("📝 卖单价格: {}", sell_order.price.unwrap().value());

    // 8. 发送订单
    order_tx.send(buy_order).await.unwrap();
    order_tx.send(sell_order).await.unwrap();
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 9. 检查成交价格
    let mut trades = Vec::new();
    while let Ok(trade) = trade_rx.try_recv() {
        trades.push(trade);
    }

    println!("💰 成交信息:");
    for trade in &trades {
        println!("   成交ID: {}", trade.id);
        println!("   方向: {:?}", trade.side);
        println!("   价格: {}", trade.price.value());
        println!("   数量: {}", trade.quantity);

        // 验证价格准确性
        match trade.side {
            OrderSide::Buy => {
                assert_eq!(
                    trade.price.value(),
                    bbo.ask_price.value(),
                    "买单应该以ask价格成交"
                );
            }
            OrderSide::Sell => {
                assert_eq!(
                    trade.price.value(),
                    bbo.bid_price.value(),
                    "卖单应该以bid价格成交"
                );
            }
        }
    }

    println!("✅ 价格准确性测试完成");
}

/// 测试订单成交时间戳一致性
#[tokio::test]
async fn test_order_trade_timestamp_consistency() {
    println!("🧪 测试订单成交时间戳一致性");
    println!("==============================");

    // 1. 初始化回测记录器
    let recorder = Arc::new(Mutex::new(backtest::types::BacktestRecorder::new()));
    set_backtest_recorder(recorder.clone()).await;
    {
        let mut recorder = recorder.lock().await;
        recorder.start_recording(Utc::now());
    }

    // 2. 创建测试环境
    let account_manager = Arc::new(Mutex::new(AccountManager::new(
        "test_account".to_string(),
        AccountConfig::default(),
    )));
    let orderbook = Arc::new(Mutex::new(OrderBook::new()));

    // 3. 创建通道
    let (market_data_tx, market_data_rx) = broadcast::channel(100);
    let (order_tx, order_rx) = mpsc::channel(100);
    let (trade_tx, mut trade_rx) = broadcast::channel(100);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);
    let (cancel_order_tx, cancel_order_rx) = mpsc::channel(100);

    // 4. 创建撮合引擎
    let mut engine = MatchingEngine::new_with_playback_config(
        orderbook,
        account_manager,
        market_data_rx,
        order_rx,
        cancel_order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
        PlaybackConfig::default(),
    );

    // 5. 发送市场数据（BBO）
    let market_timestamp = ****************; // 2022-01-01 00:00:00 UTC (微秒)
    let bbo = Bbo {
        update_id: 1,
        bid_price: Price::new(50000.0),
        bid_quantity: 1.0,
        ask_price: Price::new(50001.0),
        ask_quantity: 1.0,
        timestamp: Some(market_timestamp),
        data_source_type: DataSourceType::BinanceTardis,
    };

    let market_data = MarketData::Bbo(bbo);
    market_data_tx.send(market_data).unwrap();

    // 6. 等待市场数据处理
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 7. 创建测试订单（价格高于ask，应该立即成交）
    let order = Order {
        id: "test_order_1".to_string(),
        client_order_id: "client_test_1".to_string(),
        symbol: "BTCUSDT".to_string(),
        order_type: OrderType::Limit,
        side: OrderSide::Buy,
        price: Some(Price::new(50002.0)), // 高于ask价格，应该立即成交
        quantity: 0.1,
        status: OrderStatus::Pending,
        timestamp: DateTime::from_timestamp_micros(market_timestamp as i64).unwrap(),
        execution_info: None,
    };

    println!("📊 市场数据时间戳: {}", market_timestamp);
    println!("📝 订单时间戳: {}", order.timestamp);

    // 8. 发送订单
    order_tx.send(order).await.unwrap();

    // 9. 等待订单处理
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 10. 检查成交
    if let Ok(trade) = trade_rx.try_recv() {
        println!("💰 成交时间戳: {:?}", trade.timestamp);

        // 验证成交时间戳应该与市场数据时间戳一致
        if let Some(trade_timestamp) = trade.timestamp {
            let expected_timestamp =
                DateTime::from_timestamp_micros(market_timestamp as i64).unwrap();
            assert_eq!(
                trade_timestamp, expected_timestamp,
                "成交时间戳应该与市场数据时间戳一致"
            );
            println!("✅ 成交时间戳验证通过");
        }
    }

    // 11. 检查回测记录
    {
        let recorder = recorder.lock().await;
        if let Some(order_record) = recorder.orders.first() {
            println!("📋 记录的订单时间戳: {}", order_record.timestamp);
            let expected_timestamp =
                DateTime::from_timestamp_micros(market_timestamp as i64).unwrap();
            assert_eq!(
                order_record.timestamp, expected_timestamp,
                "订单记录时间戳应该与市场数据时间戳一致"
            );
            println!("✅ 订单时间戳验证通过");
        }

        if let Some(trade_record) = recorder.trades.first() {
            println!("💰 记录的成交时间戳: {}", trade_record.timestamp);
            let expected_timestamp =
                DateTime::from_timestamp_micros(market_timestamp as i64).unwrap();
            assert_eq!(
                trade_record.timestamp, expected_timestamp,
                "成交记录时间戳应该与市场数据时间戳一致"
            );
            println!("✅ 成交记录时间戳验证通过");
        }
    }

    println!("✅ 时间戳一致性测试完成");
}
