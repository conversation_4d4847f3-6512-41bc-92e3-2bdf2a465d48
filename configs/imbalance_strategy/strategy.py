import json
import time

import traderv2  # type: ignore
import base_strategy
import datetime


class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader: traderv2.TraderV2):
        self.trader = trader
        self.send_order = False
        self.time = None
        self.orders = {}
        self.trades = {"Buy": 0, "Sell": 0}

    def name(self):
        return "Backtest"

    def start(self):
        pass

    def subscribes(self):
        subs = [
            {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [
                        {"OrderAndFill": ["BTC_USDT"]},
                        {"Bbo": ["BTC_USDT"]},
                        {"Trade": ["BTC_USDT"]},
                        {"DepthSnapshot": {"symbols": ["BTC_USDT"], "levels": 20}},
                    ],
                },
            },
            {
                "account_id": 0,
                "sub": {"SubscribeRest": {"update_interval": {"secs": 5, "nanos": 0}, "rest_type": "Balance"}},
            },
        ]
        return subs

    def on_bbo(self, exchange, bbo):
        micros = int(bbo["timestamp"])
        self.time = datetime.datetime.fromtimestamp(micros / 1_000_000, tz=datetime.timezone.utc)
        buy_amount = float(bbo["bid_price"]) * float(bbo["bid_qty"])
        sell_amount = float(bbo["ask_price"]) * float(bbo["ask_qty"])
        if buy_amount > sell_amount * 20:
            # self.trader.log(f"bbo: {bbo}")
            order_id = self.trader.create_cid("BinanceSwap")
            order = {
                "symbol": "BTC_USDT",
                "order_type": "Limit",
                "side": "Buy",
                "price": float(bbo["bid_price"]),
                "amount": 0.001,
                "time_in_force": "PostOnly",
                "cid": order_id,
                "pos_side": None,
            }
            self.orders[order_id] = {"order": order, "time": self.time}
            return {
                "cmds": [
                    {
                        "account_id": 0,
                        "method": "PlaceOrder",
                        "order": order,
                        "params": {"is_dual_side": False, "margin_mode": "Cross", "leverage": 50},
                    }
                ]
            }
        elif buy_amount * 20 < sell_amount:
            # self.trader.log(f"bbo: {bbo}")
            order_id = self.trader.create_cid("BinanceSwap")
            order = {
                "symbol": "BTC_USDT",
                "order_type": "Limit",
                "side": "Sell",
                "price": float(bbo["ask_price"]),
                "amount": 0.001,
                "time_in_force": "PostOnly",
                "cid": order_id,
                "pos_side": None,
            }
            self.orders[order_id] = {"order": order, "time": self.time}
            return {
                "cmds": [
                    {
                        "account_id": 0,
                        "method": "PlaceOrder",
                        "order": order,
                        "params": {"is_dual_side": False, "margin_mode": "Cross", "leverage": 50},
                    }
                ]
            }

    def on_order(self, account_id, order):
        pass

    def on_trade(self, exchange, trade):
        self.trades[trade["side"]] += float(trade["amount"])
        if self.trades["Buy"] > self.trades["Sell"] * 10:
            # self.trader.log(f"Buy: {self.trades['Buy']} Sell: {self.trades['Sell']}", level="INFO", color="blue")
            self.trades = {"Buy": 0, "Sell": 0}
        elif self.trades["Sell"] > self.trades["Buy"] * 10:
            # self.trader.log(f"Sell: {self.trades['Sell']} Buy: {self.trades['Buy']}", level="INFO", color="blue")
            self.trades = {"Buy": 0, "Sell": 0}

    def on_order_submitted(self, account_id, order_id_result, order):
        # self.trader.log(f"收到订单提交回调: {order_id_result} {order}", level="INFO", color="blue")
        pass

    def on_order_and_fill(self, account_id, order):
        if order["status"] != "Open":
            pass
            # self.trader.log(f"订单/用户私有成交更新: {order}", level="INFO", color="blue")
        # else:
        #     self.trader.log(f"order id {order} ")
        #     return {
        #         "cmds": [
        #             {
        #                 "account_id": 0,
        #                 "method": "CancelOrder",
        #                 "symbol": "BTC_USDT",
        #                 "order_id": {"ClientOrderId": order["cid"]},
        #             }
        #         ]
        #     }

    def on_balance(self, account_id, balances):
        self.trader.log(f"收到余额更新: {balances}", level="INFO", color="blue")

    def on_depth(self, account_id, depth):
        self.trader.log(f"收到orderbook: {depth}", level="INFO", color="blue")
        buy_qty = 0
        sell_qty = 0
        for bid in depth["bids"]:
            buy_qty += float(bid[1])
        for ask in depth["asks"]:
            sell_qty += float(ask[1])
        if buy_qty > sell_qty * 10:
            self.trader.log(f"Buy: {buy_qty} Sell: {sell_qty}", level="INFO", color="blue")
        elif sell_qty > buy_qty * 10:
            self.trader.log(f"Sell: {sell_qty} Buy: {buy_qty}", level="INFO", color="blue")
